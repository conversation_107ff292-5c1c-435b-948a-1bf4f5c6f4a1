class TypingTutorKeyboard{constructor(t){if(this.keyElements=new Map,this.physicalKeyMap=new Map,this.layout={default:[["`","1","2","3","4","5","6","7","8","9","0","-","=","Backspace"],["Tab","q","w","e","r","t","y","u","i","o","p","[","]","\\"],["CapsLock","a","s","d","f","g","h","j","k","l",";","'","Enter"],["ShiftLeft","z","x","c","v","b","n","m",",",".","/","ShiftRight"],["Space"]],shift:[["~","!","@","#","$","%","^","&","*","(",")","_","+","Backspace"],["Tab","Q","W","E","R","T","Y","U","I","O","P","{","}","|"],["CapsLock","A","S","D","F","G","H","J","K","L",":",'"',"Enter"],["ShiftLeft","Z","X","C","V","B","N","M","<",">","?","ShiftRight"],["Space"]]},this.options=Object.assign({normalKeyColor:"#4CAF50",modifierKeyColor:"#FFC107",activeKeyColor:"#2196F3",debug:!1},t),this.state={input:"",caretPosition:0,isShiftPressed:!1,isLeftShiftPressed:!1,isRightShiftPressed:!1,isCapsLockOn:!1,isCtrlPressed:!1,isAltPressed:!1},this.container="string"==typeof t.container?document.querySelector(t.container):t.container,!this.container)throw new Error("Container element not found");this.initializePhysicalKeyMap(),this.render(),this.setupEventListeners()}initializePhysicalKeyMap(){const t={Backquote:"`",Digit1:"1",Digit2:"2",Digit3:"3",Digit4:"4",Digit5:"5",Digit6:"6",Digit7:"7",Digit8:"8",Digit9:"9",Digit0:"0",Minus:"-",Equal:"=",Backspace:"Backspace",Tab:"Tab",KeyQ:"q",KeyW:"w",KeyE:"e",KeyR:"r",KeyT:"t",KeyY:"y",KeyU:"u",KeyI:"i",KeyO:"o",KeyP:"p",BracketLeft:"[",BracketRight:"]",Backslash:"\\",CapsLock:"CapsLock",KeyA:"a",KeyS:"s",KeyD:"d",KeyF:"f",KeyG:"g",KeyH:"h",KeyJ:"j",KeyK:"k",KeyL:"l",Semicolon:";",Quote:"'",Enter:"Enter",ShiftLeft:"ShiftLeft",ShiftRight:"ShiftRight",KeyZ:"z",KeyX:"x",KeyC:"c",KeyV:"v",KeyB:"b",KeyN:"n",KeyM:"m",Comma:",",Period:".",Slash:"/",Space:"Space"};for(const[e,s]of Object.entries(t))this.physicalKeyMap.set(e,s)}render(){this.container.innerHTML="",this.container.className="typing-tutor-keyboard",this.keyElements.clear();this.getCurrentLayout().forEach(t=>{const e=document.createElement("div");e.className="keyboard-row",t.forEach(t=>{const s=this.createKeyElement(t);e.appendChild(s);const i="ShiftLeft"===t||"ShiftRight"===t||"CapsLock"===t?t:t.toLowerCase();this.keyElements.set(i,s)}),this.container.appendChild(e)}),this.updateKeyStates()}getCurrentLayout(){return this.state.isShiftPressed!==this.state.isCapsLockOn?this.layout.shift:this.layout.default}createKeyElement(t){const e=document.createElement("button");return e.className="keyboard-key",e.textContent=t,e.dataset.key=t.toLowerCase(),this.isModifierKey(t)?e.classList.add("modifier-key"):"Space"===t?e.classList.add("space-key"):t.length>1?e.classList.add("function-key"):e.classList.add("normal-key"),e.addEventListener("click",e=>{e.preventDefault(),this.handleKeyPress(t,e)}),e}isModifierKey(t){return["ShiftLeft","ShiftRight","CapsLock","Ctrl","Alt","Tab"].includes(t)}setupEventListeners(){document.addEventListener("keydown",this.handlePhysicalKeyDown.bind(this)),document.addEventListener("keyup",this.handlePhysicalKeyUp.bind(this))}handlePhysicalKeyDown(t){const e=this.physicalKeyMap.get(t.code);e&&(this.updateModifierStates(t),this.highlightKey(e,!0),1===e.length||["Backspace","Enter","Space","Tab"].includes(e)||"CapsLock"===e?(t.preventDefault(),this.handleKeyPress(e,t)):this.handleKeyPress(e,t),this.options.debug&&console.log("Physical key down:",t.code,"->",e))}handlePhysicalKeyUp(t){const e=this.physicalKeyMap.get(t.code);e&&(this.updateModifierStates(t),"CapsLock"===e&&this.state.isCapsLockOn||this.highlightKey(e,!1),this.options.onKeyRelease&&this.options.onKeyRelease(e,t),this.options.debug&&console.log("Physical key up:",t.code,"->",e))}updateModifierStates(t){const e=this.state.isShiftPressed,s=this.state.isCapsLockOn;this.state.isShiftPressed=t.shiftKey,"keydown"===t.type?"ShiftLeft"===t.code?this.state.isLeftShiftPressed=!0:"ShiftRight"===t.code&&(this.state.isRightShiftPressed=!0):"keyup"===t.type&&("ShiftLeft"===t.code?this.state.isLeftShiftPressed=!1:"ShiftRight"===t.code&&(this.state.isRightShiftPressed=!1)),this.state.isCtrlPressed=t.ctrlKey,this.state.isAltPressed=t.altKey,"CapsLock"===t.code&&"keydown"===t.type&&(this.state.isCapsLockOn=!this.state.isCapsLockOn,this.state.isCapsLockOn?this.highlightKey("CapsLock",!0):this.highlightKey("CapsLock",!1)),e===this.state.isShiftPressed&&s===this.state.isCapsLockOn||this.render()}highlightKey(t,e){const s="ShiftLeft"===t||"ShiftRight"===t||"CapsLock"===t?t:t.toLowerCase(),i=this.keyElements.get(s);if(i)if(e){const e=this.isModifierKey(t)?this.options.modifierKeyColor:this.options.normalKeyColor;i.style.backgroundColor=e||"#4CAF50",i.classList.add("highlighted")}else i.style.backgroundColor="",i.classList.remove("highlighted")}handleKeyPress(t,e){switch(this.options.onKeyPress&&this.options.onKeyPress(t,e),t){case"Backspace":this.handleBackspace();break;case"Enter":this.handleEnter();break;case"Space":this.handleSpace();break;case"Tab":this.handleTab();break;case"ShiftLeft":case"ShiftRight":case"CapsLock":break;default:1===t.length&&this.handleCharacterInput(t)}this.updateKeyStates()}handleBackspace(){if(this.state.input.length>0&&this.state.caretPosition>0){const t=this.state.input.substring(0,this.state.caretPosition-1),e=this.state.input.substring(this.state.caretPosition);this.state.input=t+e,this.state.caretPosition--,this.notifyChange()}}handleEnter(){this.state.input+="\n",this.state.caretPosition++,this.notifyChange()}handleSpace(){this.state.input+=" ",this.state.caretPosition++,this.notifyChange()}handleTab(){this.state.input+="\t",this.state.caretPosition++,this.notifyChange()}handleCharacterInput(t){const e=this.transformCharacter(t),s=this.state.input.substring(0,this.state.caretPosition),i=this.state.input.substring(this.state.caretPosition);this.state.input=s+e+i,this.state.caretPosition++,this.notifyChange()}transformCharacter(t){if(!(this.state.isShiftPressed!==this.state.isCapsLockOn))return t.toLowerCase();return/^[a-z]$/i.test(t)?t.toUpperCase():{"`":"~",1:"!",2:"@",3:"#",4:"$",5:"%",6:"^",7:"&",8:"*",9:"(",0:")","-":"_","=":"+","[":"{","]":"}","\\":"|",";":":","'":'"',",":"<",".":">","/":"?"}[t]||t}updateKeyStates(){this.keyElements.forEach((t,e)=>{t.classList.remove("active-modifier"),("ShiftLeft"===e&&this.state.isLeftShiftPressed||"ShiftRight"===e&&this.state.isRightShiftPressed||"CapsLock"===e&&this.state.isCapsLockOn)&&t.classList.add("active-modifier")})}notifyChange(){this.options.onChange&&this.options.onChange(this.state.input)}getInput(){return this.state.input}setInput(t){this.state.input=t,this.state.caretPosition=t.length,this.notifyChange()}clearInput(){this.state.input="",this.state.caretPosition=0,this.notifyChange()}destroy(){document.removeEventListener("keydown",this.handlePhysicalKeyDown.bind(this)),document.removeEventListener("keyup",this.handlePhysicalKeyUp.bind(this)),this.container.innerHTML="",this.keyElements.clear()}}export default TypingTutorKeyboard;