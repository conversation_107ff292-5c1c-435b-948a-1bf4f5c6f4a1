<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typing Tutor Keyboard Demo</title>
    <link rel="stylesheet" href="typing-tutor-keyboard.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .input-display {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f8f9fa;
            resize: vertical;
            line-height: 1.5;
        }

        .input-display:focus {
            outline: none;
            border-color: #2196F3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            flex: 1;
            min-width: 120px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #2196F3;
            color: white;
        }

        .btn-primary:hover {
            background: #1976D2;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .keyboard-section {
            margin-top: 30px;
        }

        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }

        .debug-info {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .debug-info h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }

        .instructions {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 6px 6px 0;
        }

        .instructions h3 {
            margin: 0 0 10px 0;
            color: #1976D2;
        }

        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }

        .instructions li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            h1 {
                font-size: 2em;
            }

            .stats {
                flex-direction: column;
            }

            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎹 Typing Tutor Keyboard</h1>
        <p class="subtitle">Minimal keyboard with automatic shift/caps lock synchronization for young learners</p>

        <div class="instructions">
            <h3>📚 How to Use</h3>
            <ul>
                <li><strong>Green keys:</strong> Normal character keys when pressed on physical keyboard</li>
                <li><strong>Yellow keys:</strong> Modifier keys (Shift, Caps Lock) when pressed</li>
                <li><strong>Blue keys:</strong> Function keys (Backspace, Enter, etc.) when pressed</li>
                <li>Try pressing Shift or Caps Lock to see the layout change automatically!</li>
                <li>Both virtual and physical keyboard input work seamlessly together</li>
            </ul>
        </div>

        <div class="input-section">
            <label class="input-label">Type here (or use the virtual keyboard below):</label>
            <textarea class="input-display" id="textInput" placeholder="Start typing to see the magic happen! Try pressing Shift or Caps Lock..."></textarea>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-label">Characters</div>
                <div class="stat-value" id="charCount">0</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Words</div>
                <div class="stat-value" id="wordCount">0</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Shift State</div>
                <div class="stat-value" id="shiftState">Normal</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Caps Lock</div>
                <div class="stat-value" id="capsState">Off</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="insertSampleText()">📝 Insert Sample Text</button>
            <button class="btn btn-secondary" onclick="toggleDebug()">🔍 Toggle Debug</button>
            <button class="btn btn-danger" onclick="clearText()">🗑️ Clear Text</button>
        </div>

        <div class="keyboard-section">
            <h2 class="section-title">Virtual Keyboard</h2>
            <div id="keyboard-container"></div>
        </div>

        <div class="debug-info" id="debugInfo" style="display: none;">
            <h4>🐛 Debug Information</h4>
            <div id="debugContent"></div>
        </div>
    </div>

    <script type="module">
        import TypingTutorKeyboard from './dist/typing-tutor-keyboard.js';

        let keyboard;
        let debugMode = false;

        // Initialize the keyboard
        function initKeyboard() {
            keyboard = new TypingTutorKeyboard({
                container: '#keyboard-container',
                debug: debugMode,
                onChange: (input) => {
                    const textInput = document.getElementById('textInput');
                    textInput.value = input;
                    updateStats();
                    if (debugMode) {
                        logDebug(`Input changed: "${input}"`);
                    }
                },
                onKeyPress: (key, event) => {
                    if (debugMode) {
                        logDebug(`Key pressed: ${key} (${event ? event.type : 'virtual'})`);
                    }
                },
                onKeyRelease: (key, event) => {
                    if (debugMode) {
                        logDebug(`Key released: ${key}`);
                    }
                },
                onStateChange: (state) => {
                    // Update modifier states when keyboard state changes
                    document.getElementById('shiftState').textContent = state.isShiftPressed ? 'Pressed' : 'Normal';
                    document.getElementById('capsState').textContent = state.isCapsLockOn ? 'On' : 'Off';

                    if (debugMode) {
                        logDebug(`State changed: Shift=${state.isShiftPressed}, Caps=${state.isCapsLockOn}`);
                    }
                }
            });
        }

        // Demo functions
        window.insertSampleText = function() {
            const sampleText = 'The quick brown fox jumps over the lazy dog. HELLO WORLD! 123456789';
            keyboard.setInput(sampleText);
            updateStats();
        };

        window.clearText = function() {
            keyboard.clearInput();
            updateStats();
        };

        window.toggleDebug = function() {
            debugMode = !debugMode;
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.style.display = debugMode ? 'block' : 'none';

            if (debugMode) {
                logDebug('Debug mode enabled');
            }
        };

        function updateStats() {
            const text = keyboard ? keyboard.getInput() : '';

            document.getElementById('charCount').textContent = text.length;
            document.getElementById('wordCount').textContent = text.trim() ? text.trim().split(/\s+/).length : 0;
        }



        function logDebug(message) {
            if (!debugMode) return;

            const debugContent = document.getElementById('debugContent');
            const timestamp = new Date().toLocaleTimeString();
            debugContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            debugContent.scrollTop = debugContent.scrollHeight;
        }

        // Sync text input with keyboard
        document.getElementById('textInput').addEventListener('input', function(e) {
            if (keyboard) {
                keyboard.setInput(e.target.value);
            }
            updateStats();
        });

        // Remove periodic updates - we'll use event-driven updates instead

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initKeyboard();
            updateStats();
        });
    </script>
</body>
</html>
