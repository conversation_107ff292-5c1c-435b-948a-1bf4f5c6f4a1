{"name": "simple-keyboard", "version": "3.8.64", "description": "On-screen Javascript Virtual Keyboard", "main": "build/index.js", "scripts": {"start": "webpack serve --config webpack.config.demo.js", "build": "webpack && npm run build-modern && npm run build-modern-esm && tsc", "build-modern": "webpack --config webpack.config.modern.js", "build-modern-esm": "webpack --config webpack.config.modern_esm.js", "test": "jest --silent", "coverage": "npm run test -- --coverage", "prepare": "npm run build"}, "repository": {"type": "git", "url": "https://github.com/hodgef/simple-keyboard"}, "author": "<PERSON> <<EMAIL>> (https://github.com/hodgef)", "bugs": {"url": "https://github.com/hodgef/simple-keyboard/issues"}, "homepage": "https://virtual-keyboard.js.org/", "keywords": ["javascript", "es6", "digital", "keyboard", "onscreen", "virtual", "screen-keyboard", "component", "virtual-keyboard", "touchscreen", "touch-screen", "kiosk", "osk", "js"], "license": "MIT", "devDependencies": {"@babel/cli": "^7.28.0", "@babel/core": "^7.27.7", "@babel/plugin-proposal-class-properties": "^7.17.12", "@babel/plugin-transform-typescript": "^7.28.0", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.28.0", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.21", "babel-eslint": "^10.1.0", "babel-loader": "^9.2.1", "babel-preset-minify": "^0.5.2", "core-js": "^3.43.0", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "eslint": "^8.57.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.14", "typescript": "^5.8.3", "url-loader": "^4.1.1", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "4.15.0"}, "jest": {"testEnvironment": "jsdom", "roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/lib/index.js", "!src/lib/polyfills.js", "!src/demo/**", "!src/utils/**", "!src/**/*.d.ts", "!**/tests/**"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/scripts/testMock.js", "\\.(css|less)$": "<rootDir>/scripts/testMock.js"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"]}}