/**
 * Minimal Typing Tutor Keyboard
 * Focused implementation for young children's typing education
 * with automatic shift/caps lock synchronization
 */

interface TypingTutorOptions {
  container: string | HTMLElement;
  onChange?: (input: string) => void;
  onKeyPress?: (key: string, event?: Event) => void;
  onKeyRelease?: (key: string, event?: Event) => void;
  normalKeyColor?: string;
  modifierKeyColor?: string;
  activeKeyColor?: string;
  debug?: boolean;
}

interface KeyboardState {
  input: string;
  caretPosition: number;
  isShiftPressed: boolean;
  isCapsLockOn: boolean;
  isCtrlPressed: boolean;
  isAltPressed: boolean;
}

class TypingTutorKeyboard {
  private container: HTMLElement;
  private options: TypingTutorOptions;
  private state: KeyboardState;
  private keyElements: Map<string, HTMLElement> = new Map();
  private physicalKeyMap: Map<string, string> = new Map();

  // Basic QWERTY layout
  private readonly layout = {
    default: [
      ['`', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', 'Backspace'],
      ['Tab', 'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
      ['CapsLock', 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'", 'Enter'],
      ['ShiftLeft', 'z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/', 'ShiftRight'],
      ['Space']
    ],
    shift: [
      ['~', '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', 'Backspace'],
      ['Tab', 'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '{', '}', '|'],
      ['CapsLock', 'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ':', '"', 'Enter'],
      ['ShiftLeft', 'Z', 'X', 'C', 'V', 'B', 'N', 'M', '<', '>', '?', 'ShiftRight'],
      ['Space']
    ]
  };

  constructor(options: TypingTutorOptions) {
    this.options = {
      normalKeyColor: '#4CAF50',    // Green for normal keys
      modifierKeyColor: '#FFC107',  // Yellow for modifier keys
      activeKeyColor: '#2196F3',    // Blue for active keys
      debug: false,
      ...options
    };

    this.state = {
      input: '',
      caretPosition: 0,
      isShiftPressed: false,
      isCapsLockOn: false,
      isCtrlPressed: false,
      isAltPressed: false
    };

    this.container = typeof options.container === 'string' 
      ? document.querySelector(options.container) as HTMLElement
      : options.container;

    if (!this.container) {
      throw new Error('Container element not found');
    }

    this.initializePhysicalKeyMap();
    this.render();
    this.setupEventListeners();
  }

  private initializePhysicalKeyMap(): void {
    // Map physical keyboard codes to our key names
    const keyMap = {
      'Backquote': '`',
      'Digit1': '1', 'Digit2': '2', 'Digit3': '3', 'Digit4': '4', 'Digit5': '5',
      'Digit6': '6', 'Digit7': '7', 'Digit8': '8', 'Digit9': '9', 'Digit0': '0',
      'Minus': '-', 'Equal': '=', 'Backspace': 'Backspace',
      'Tab': 'Tab',
      'KeyQ': 'q', 'KeyW': 'w', 'KeyE': 'e', 'KeyR': 'r', 'KeyT': 't',
      'KeyY': 'y', 'KeyU': 'u', 'KeyI': 'i', 'KeyO': 'o', 'KeyP': 'p',
      'BracketLeft': '[', 'BracketRight': ']', 'Backslash': '\\',
      'CapsLock': 'CapsLock',
      'KeyA': 'a', 'KeyS': 's', 'KeyD': 'd', 'KeyF': 'f', 'KeyG': 'g',
      'KeyH': 'h', 'KeyJ': 'j', 'KeyK': 'k', 'KeyL': 'l',
      'Semicolon': ';', 'Quote': "'", 'Enter': 'Enter',
      'ShiftLeft': 'ShiftLeft', 'ShiftRight': 'ShiftRight',
      'KeyZ': 'z', 'KeyX': 'x', 'KeyC': 'c', 'KeyV': 'v', 'KeyB': 'b',
      'KeyN': 'n', 'KeyM': 'm', 'Comma': ',', 'Period': '.', 'Slash': '/',
      'Space': 'Space'
    };

    for (const [physicalKey, virtualKey] of Object.entries(keyMap)) {
      this.physicalKeyMap.set(physicalKey, virtualKey);
    }
  }

  private render(): void {
    this.container.innerHTML = '';
    this.container.className = 'typing-tutor-keyboard';
    this.keyElements.clear();

    const currentLayout = this.getCurrentLayout();

    currentLayout.forEach((row, rowIndex) => {
      const rowElement = document.createElement('div');
      rowElement.className = 'keyboard-row';

      row.forEach(key => {
        const keyElement = this.createKeyElement(key);
        rowElement.appendChild(keyElement);
        // Use the exact key name for shift keys to distinguish left/right
        const keyMapName = (key === 'ShiftLeft' || key === 'ShiftRight') ? key : key.toLowerCase();
        this.keyElements.set(keyMapName, keyElement);
      });

      this.container.appendChild(rowElement);
    });

    this.updateKeyStates();
  }

  private getCurrentLayout(): string[][] {
    const shouldUseShift = this.state.isShiftPressed !== this.state.isCapsLockOn;
    return shouldUseShift ? this.layout.shift : this.layout.default;
  }

  private createKeyElement(key: string): HTMLElement {
    const element = document.createElement('button');
    element.className = 'keyboard-key';
    element.textContent = key;
    element.dataset.key = key.toLowerCase();

    // Add special classes for different key types
    if (this.isModifierKey(key)) {
      element.classList.add('modifier-key');
    } else if (key === 'Space') {
      element.classList.add('space-key');
    } else if (key.length > 1) {
      element.classList.add('function-key');
    } else {
      element.classList.add('normal-key');
    }

    // Add click handler
    element.addEventListener('click', (e) => {
      e.preventDefault();
      this.handleKeyPress(key, e);
    });

    return element;
  }

  private isModifierKey(key: string): boolean {
    return ['ShiftLeft', 'ShiftRight', 'CapsLock', 'Ctrl', 'Alt', 'Tab'].includes(key);
  }

  private setupEventListeners(): void {
    // Physical keyboard listeners
    document.addEventListener('keydown', this.handlePhysicalKeyDown.bind(this));
    document.addEventListener('keyup', this.handlePhysicalKeyUp.bind(this));
  }

  private handlePhysicalKeyDown(event: KeyboardEvent): void {
    const virtualKey = this.physicalKeyMap.get(event.code);
    
    if (!virtualKey) return;

    // Update modifier states
    this.updateModifierStates(event);

    // Highlight the corresponding virtual key
    this.highlightKey(virtualKey, true);

    // Handle the key press
    this.handleKeyPress(virtualKey, event);

    if (this.options.debug) {
      console.log('Physical key down:', event.code, '->', virtualKey);
    }
  }

  private handlePhysicalKeyUp(event: KeyboardEvent): void {
    const virtualKey = this.physicalKeyMap.get(event.code);
    
    if (!virtualKey) return;

    // Update modifier states
    this.updateModifierStates(event);

    // Remove highlight from the virtual key
    this.highlightKey(virtualKey, false);

    if (this.options.onKeyRelease) {
      this.options.onKeyRelease(virtualKey, event);
    }

    if (this.options.debug) {
      console.log('Physical key up:', event.code, '->', virtualKey);
    }
  }

  private updateModifierStates(event: KeyboardEvent): void {
    const prevShift = this.state.isShiftPressed;
    const prevCaps = this.state.isCapsLockOn;

    this.state.isShiftPressed = event.shiftKey;
    this.state.isCtrlPressed = event.ctrlKey;
    this.state.isAltPressed = event.altKey;

    // Handle caps lock toggle
    if (event.code === 'CapsLock' && event.type === 'keydown') {
      this.state.isCapsLockOn = !this.state.isCapsLockOn;
    }

    // Re-render if shift or caps lock state changed
    if (prevShift !== this.state.isShiftPressed || prevCaps !== this.state.isCapsLockOn) {
      this.render();
    }
  }

  private highlightKey(key: string, highlight: boolean): void {
    // Use the exact key name for shift keys to distinguish left/right
    const keyMapName = (key === 'ShiftLeft' || key === 'ShiftRight') ? key : key.toLowerCase();
    const element = this.keyElements.get(keyMapName);
    if (!element) return;

    if (highlight) {
      const color = this.isModifierKey(key) 
        ? this.options.modifierKeyColor 
        : this.options.normalKeyColor;
      
      element.style.backgroundColor = color;
      element.classList.add('highlighted');
    } else {
      element.style.backgroundColor = '';
      element.classList.remove('highlighted');
    }
  }

  private handleKeyPress(key: string, event?: Event): void {
    if (this.options.onKeyPress) {
      this.options.onKeyPress(key, event);
    }

    // Handle special keys
    switch (key) {
      case 'Backspace':
        this.handleBackspace();
        break;
      case 'Enter':
        this.handleEnter();
        break;
      case 'Space':
        this.handleSpace();
        break;
      case 'Tab':
        this.handleTab();
        break;
      case 'ShiftLeft':
      case 'ShiftRight':
      case 'CapsLock':
        // These are handled in updateModifierStates
        break;
      default:
        if (key.length === 1) {
          this.handleCharacterInput(key);
        }
        break;
    }

    this.updateKeyStates();
  }

  private handleBackspace(): void {
    if (this.state.input.length > 0 && this.state.caretPosition > 0) {
      const before = this.state.input.substring(0, this.state.caretPosition - 1);
      const after = this.state.input.substring(this.state.caretPosition);
      this.state.input = before + after;
      this.state.caretPosition--;
      this.notifyChange();
    }
  }

  private handleEnter(): void {
    this.state.input += '\n';
    this.state.caretPosition++;
    this.notifyChange();
  }

  private handleSpace(): void {
    this.state.input += ' ';
    this.state.caretPosition++;
    this.notifyChange();
  }

  private handleTab(): void {
    this.state.input += '\t';
    this.state.caretPosition++;
    this.notifyChange();
  }

  private handleCharacterInput(char: string): void {
    const before = this.state.input.substring(0, this.state.caretPosition);
    const after = this.state.input.substring(this.state.caretPosition);
    this.state.input = before + char + after;
    this.state.caretPosition++;
    this.notifyChange();
  }

  private updateKeyStates(): void {
    // Update visual state of modifier keys
    this.keyElements.forEach((element, key) => {
      element.classList.remove('active-modifier');

      if ((key === 'ShiftLeft' || key === 'ShiftRight') && this.state.isShiftPressed) {
        element.classList.add('active-modifier');
      } else if (key === 'capslock' && this.state.isCapsLockOn) {
        element.classList.add('active-modifier');
      }
    });
  }

  private notifyChange(): void {
    if (this.options.onChange) {
      this.options.onChange(this.state.input);
    }
  }

  // Public API methods
  public getInput(): string {
    return this.state.input;
  }

  public setInput(input: string): void {
    this.state.input = input;
    this.state.caretPosition = input.length;
    this.notifyChange();
  }

  public clearInput(): void {
    this.state.input = '';
    this.state.caretPosition = 0;
    this.notifyChange();
  }

  public destroy(): void {
    document.removeEventListener('keydown', this.handlePhysicalKeyDown.bind(this));
    document.removeEventListener('keyup', this.handlePhysicalKeyUp.bind(this));
    this.container.innerHTML = '';
    this.keyElements.clear();
  }
}

export default TypingTutorKeyboard;
