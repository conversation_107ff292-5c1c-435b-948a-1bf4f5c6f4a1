{"version": 3, "file": "index.modern.js", "mappings": ";;;;;;;;;;;;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAwB,eAAID,IAE5BD,EAAqB,eAAIC,GAC1B,CATD,CASGK,KAAM,WACT,O,wBCTA,IAAIC,EAAsB,CCA1BA,EAAwB,SAASL,EAASM,GACzC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAG3E,ECPAF,EAAwB,SAASQ,EAAKC,GAAQ,OAAOL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,EAAO,ECCtGT,EAAwB,SAASL,GACX,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,GACvD,G,0nDCHA,IAGMC,EAAS,WA2Bb,O,EAjBA,SAAAA,EAAAC,GAKoB,IAJlBC,EAAUD,EAAVC,WACAC,EAAgBF,EAAhBE,iBACAC,EAAmBH,EAAnBG,oBACAC,EAAQJ,EAARI,U,4FAAQC,CAAA,KAAAN,GAAAO,EAAA,0BAAAA,EAAA,gCAAAA,EAAA,mCAAAA,EAAA,wBAAAA,EAAA,gCA6RVA,EAAA,wBAGmB,SAACC,GAAc,OAChCA,KAA0B,MAAdA,EAAO,IAA4C,MAA9BA,EAAOA,EAAOC,OAAS,GAAW,GA/RnE1B,KAAKmB,WAAaA,EAClBnB,KAAKoB,iBAAmBA,EACxBpB,KAAKqB,oBAAsBA,EAC3BrB,KAAKsB,SAAWA,EAKhBL,EAAUU,YAAYV,EAAWjB,KACnC,E,EAEA,EAAAG,IAAA,gBAAAa,MAMA,SAAcS,GACZ,OAAOA,EAAOG,SAAS,MAAQH,EAAOG,SAAS,MAAmB,SAAXH,EACnD,cACA,aACN,GAEA,CAAAtB,IAAA,iBAAAa,MAMA,SAAeS,GACb,IAAMI,EAAkB7B,KAAK8B,cAAcL,GACrCM,EAAsBN,EAAOO,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAC7DC,EAAmB,GAKvB,MAHwB,gBAApBJ,IACFI,EAAmB,cAAHC,OAAiBH,IAE5B,MAAPG,OAAaL,GAAeK,OAAGD,EACjC,GAEA,CAAA9B,IAAA,mBAAAa,MAGA,WACE,MAAO,CACL,SAAU,YACV,cAAe,YACf,UAAW,UACX,UAAW,QACX,cAAe,QACf,eAAgB,QAChB,QAAS,MACT,MAAO,QACP,QAAS,MACT,SAAU,OACV,aAAc,OACd,WAAY,SACZ,UAAW,IACX,OAAQ,IACR,QAAS,MACT,WAAY,MACZ,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,QAAS,MACT,QAAS,MACT,QAAS,MACT,iBAAkB,IAClB,YAAa,OACb,YAAa,IACb,cAAe,IACf,cAAe,IACf,eAAgB,IAChB,WAAY,QACZ,eAAgB,SAChB,UAAW,QACX,WAAY,MACZ,SAAU,OACV,WAAY,KACZ,WAAY,MACZ,kBAAmB,MACnB,QAAS,MACT,aAAc,OACd,mBAAoB,IACpB,mBAAoB,IACpB,cAAe,IACf,gBAAiB,QACjB,WAAY,IACZ,kBAAmB,IACnB,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IAEjB,GACA,CAAAb,IAAA,uBAAAa,MAOA,SACES,EACAU,GASA,OALEA,EAHUC,UAAAV,OAAA,QAAAW,IAAAD,UAAA,IAAAA,UAAA,GAGA/B,OAAOiC,OAAO,CAAC,EAAGtC,KAAKuC,mBAAoBJ,GAE3CA,GAAWnC,KAAKuC,oBAGbd,IAAWA,CAC5B,GAEA,CAAAtB,IAAA,kBAAAa,MASA,SACES,EACAe,EACAC,GAGA,IAFAC,EAAWN,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAGK,EACdE,EAASP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,IAAAA,UAAA,GAEHQ,EAAU5C,KAAKmB,aACf0B,EAAkE,CACtEJ,EACAC,EACAC,GAGEG,EAASN,EAuDb,OApDc,WAAXf,GAAkC,gBAAXA,IACxBqB,EAAOpB,OAAS,EAEhBoB,EAAS9C,KAAK+C,SAAQC,MAAbhD,KAAI,CAAU8C,GAAMZ,OAAKW,KAEtB,aAAXpB,GAAoC,oBAAXA,IAC1BqB,EAAOpB,OAAS,EAEhBoB,EAAS9C,KAAKiD,iBAAgBD,MAArBhD,KAAI,CAAkB8C,GAAMZ,OAAKW,IACtB,YAAXpB,EACTqB,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CAAa8C,EAAQ,KAAGZ,OAAKW,IAE/B,UAAXpB,GAEkC,kBAAzBmB,EAAQO,eACU,IAAzBP,EAAQO,aAKE,YAAX1B,GAAmC,kBAAXA,IACzBmB,EAAQQ,eAIR3B,EAAOG,SAAS,WAChByB,OAAOC,UAAUD,OAAO5B,EAAOA,EAAOC,OAAS,KAE/CoB,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CACX8C,EACArB,EAAOA,EAAOC,OAAS,IAAEQ,OACtBW,IAEe,mBAAXpB,EACTqB,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CAAa8C,EAAQ,KAAGZ,OAAKW,IACxB,qBAAXpB,EACPqB,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CAAa8C,EAAQ,KAAGZ,OAAKW,IACxB,qBAAXpB,EACPqB,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CAAa8C,EAAQ,KAAGZ,OAAKW,IACxB,gBAAXpB,EACPqB,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CAAa8C,EAAQ,KAAGZ,OAAKW,IACxB,oBAAXpB,EACPqB,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CAAa8C,EAAQ,KAAGZ,OAAKW,IACxB,MAAXpB,GAA6B,MAAXA,EACzBqB,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CAAa8C,EAAQrB,GAAMS,OAAKW,IACrCpB,EAAOG,SAAS,MAASH,EAAOG,SAAS,OACjDkB,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CAAa8C,EAAQrB,GAAMS,OAAKW,KAvB7CC,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CAAa8C,EAAQ,MAAIZ,OAAKW,IAL3CC,EAAS9C,KAAKkD,YAAWF,MAAhBhD,KAAI,CAAa8C,EAAQ,MAAIZ,OAAKW,IA8B1CD,EAAQW,OACTC,QAAQC,IAAI,kBAAmBX,GAG1BA,CACT,GAEA,CAAA3C,IAAA,iBAAAa,MAMA,SAAeU,GAA+B,IAAfgC,EAAKtB,UAAAV,OAAA,QAAAW,IAAAD,UAAA,IAAAA,UAAA,GAC5BuB,EAAc3D,KAAK4D,qBAAqBlC,EAAQgC,GAEtD1D,KAAKsB,SAAS,SAACuC,GACbA,EAASC,iBAAiBH,EAC5B,EACF,GAEA,CAAAxD,IAAA,uBAAAa,MAMA,SAAqBU,GAA+B,IAAfgC,EAAKtB,UAAAV,OAAA,QAAAW,IAAAD,UAAA,IAAAA,UAAA,GAClCQ,EAAU5C,KAAKmB,aACjB4C,EAAgB/D,KAAKoB,mBAczB,OAZqB,MAAjB2C,IACEL,EACEK,EAAgB,IAAGA,GAAgCrC,GAEvDqC,GAAgCrC,GAIhCkB,EAAQW,OACVC,QAAQC,IAAI,YAAaM,GAGpBA,CACT,GAEA,CAAA5D,IAAA,cAAAa,MAQA,SACEgD,EACAC,GAIA,IACInB,EAJJoB,EAAQ9B,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG4B,EAAOtC,OAClByC,EAAW/B,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG4B,EAAOtC,OACrBiB,EAASP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,IAAAA,UAAA,GAmBT,OAfK8B,GAAyB,IAAbA,GAGfpB,EAAS,CAACkB,EAAOI,MAAM,EAAGF,GAAWD,EAAKD,EAAOI,MAAMD,IAAcE,KACnE,IAMGrE,KAAKsE,sBACJ3B,GAAW3C,KAAKuE,eAAeN,EAAIvC,SAVzCoB,EAASkB,EAASC,EAcbnB,CACT,GAAC,CAAA3C,IAAA,WAAAa,MAeD,SACEgD,GAIA,IAKIlB,EARJoB,EAAQ9B,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG4B,EAAOtC,OAClByC,EAAW/B,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG4B,EAAOtC,OACrBiB,EAASP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,IAAAA,UAAA,GAET,GAAiB,IAAb8B,GAAkC,IAAhBC,EACpB,OAAOH,EAKT,GAAIE,IAAaC,EAAa,CAC5B,IAEMK,EAAkB,oCAMpBN,GAAYA,GAAY,EACXF,EAAOS,UAAUP,EAAW,EAAGA,GAClBQ,MAAMF,IAGhC1B,EAASkB,EAAOW,OAAO,EAAGT,EAAW,GAAKF,EAAOW,OAAOT,GACpDvB,GAAW3C,KAAKuE,eAAe,GAAG,KAEtCzB,EAASkB,EAAOW,OAAO,EAAGT,EAAW,GAAKF,EAAOW,OAAOT,GACpDvB,GAAW3C,KAAKuE,eAAe,GAAG,IAGzBP,EAAOI,OAAO,GACDM,MAAMF,IAGhC1B,EAASkB,EAAOI,MAAM,GAAI,GACtBzB,GAAW3C,KAAKuE,eAAe,GAAG,KAEtCzB,EAASkB,EAAOI,MAAM,GAAI,GACtBzB,GAAW3C,KAAKuE,eAAe,GAAG,GAG5C,MACEzB,EAASkB,EAAOI,MAAM,EAAGF,GAAYF,EAAOI,MAAMD,GAC9CxB,GACF3C,KAAKsB,SAAS,SAACuC,GACbA,EAASC,iBAAiBI,EAC5B,GAIJ,OAAOpB,CACT,GAEA,CAAA3C,IAAA,mBAAAa,MAMA,SACEgD,GAIA,IAKIlB,EARJoB,EAAgB9B,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG4B,EAAOtC,OAC1ByC,EAAmB/B,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG4B,EAAOtC,OAC7BiB,EAASP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,IAAAA,UAAA,GAET,OAAK4B,SAAAA,EAAQtC,QAAuB,OAAbwC,GAMnBA,IAAaC,EAWbrB,EAJmBkB,EAAOS,UAAUP,EAAUA,EAAW,GACzBQ,MAPV,qCAUbV,EAAOW,OAAO,EAAGT,GAAYF,EAAOW,OAAOT,EAAW,GAEtDF,EAAOW,OAAO,EAAGT,GAAYF,EAAOW,OAAOT,EAAW,IAGjEpB,EAASkB,EAAOI,MAAM,EAAGF,GAAYF,EAAOI,MAAMD,GAC9CxB,GACF3C,KAAKsB,SAAS,SAACuC,GACbA,EAASC,iBAAiBI,EAC5B,IAIGpB,GA7BEkB,CA8BX,GAEA,CAAA7D,IAAA,kBAAAa,MAMA,SAAgB4D,EAAyBC,GACvC,IAAMjC,EAAU5C,KAAKmB,aACf2D,EAAYlC,EAAQkC,UACpBC,EAAeH,EAAShC,EAAQoC,WAAa,WAC7CC,EAAYJ,EAAanD,OAAS,GAAKoD,EAE7C,GAKED,EAAanD,QAAUqD,EAAarD,OAEpC,OAAO,EAGT,GAAI2B,OAAOC,UAAUwB,GAKnB,OAJIlC,EAAQW,OACVC,QAAQC,IAAI,2BAA4BwB,GAGtCA,GAIFjF,KAAKkF,kBAAmB,GACjB,IAEPlF,KAAKkF,kBAAmB,GACjB,GAIX,GAAyB,WAArBC,EAAOL,GAAwB,CACjC,IAAMG,EACJJ,EAAanD,OAAS,GAAKoD,EAAUlC,EAAQoC,WAAa,WAM5D,OAJIpC,EAAQW,OACVC,QAAQC,IAAI,2BAA4BwB,GAGtCA,GACFjF,KAAKkF,kBAAmB,GACjB,IAEPlF,KAAKkF,kBAAmB,GACjB,EAEX,CACF,GAEA,CAAA/E,IAAA,qBAAAa,MAGA,WACE,OAAOoE,QAAQpF,KAAKkF,iBACtB,GAEA,CAAA/E,IAAA,gBAAAa,MAGA,WACE,MAAO,iBAAkBqE,QAAUC,UAAUC,cAC/C,GAEA,CAAApF,IAAA,yBAAAa,MAGA,WACE,QAASqE,OAAOG,YAClB,GAEA,CAAArF,IAAA,YAAAa,MAoBA,SAAUiD,GACR,OAAKA,EAEEA,EACJwB,cACAC,OACAC,MAAM,aACNC,OAAO,SAAC3B,EAAK4B,GAAI,OAChBA,EAAKnE,OAASuC,EAAM4B,EAAK,GAAGC,cAAgBD,EAAKzB,MAAM,GAAKH,CAAG,GAPlD,EASnB,GAEA,CAAA9D,IAAA,aAAAa,MAGA,SAAc+E,EAAUC,GACtB,OAAOC,EAAIC,MAAMC,KAAKC,KAAKL,EAAIrE,OAASsE,KAAQK,IAAI,SAACC,EAAGC,GAAC,OACvDR,EAAI3B,MAAM4B,EAAOO,EAAGP,EAAOA,EAAOO,EAAE,EAExC,GAEA,CAAApG,IAAA,cAAAa,MAGA,SAAYiD,GACV,OAAOA,EAAIjC,QAAQ,yBAA0B,OAC/C,GAEA,CAAA7B,IAAA,eAAAa,MAGA,SAAawF,EAAehE,GAC1B,IAAIiE,EAAWD,EACTE,EAAmBlE,EAAMmE,QAAQ,KAMvC,OAHGD,EAAmBF,IAA8B,GAArBE,GAAyBD,IAFjCjE,EAAMmE,QAAQ,KAGjBH,IAA8B,GAArBE,GAAyBD,IAE/CA,EAAW,EAAI,EAAIA,CAC5B,GAEA,CAAAtG,IAAA,gBAAAa,MAQA,SAAc4F,GACZ,IACEC,QAAQC,UAAUC,OAAQ,GAAIH,EAChC,CAAE,MAAOI,GACP,OAAO,CACT,CACA,OAAO,CACT,I,EAAC,EAAA7G,IAAA,cAAAa,MAzED,SAAmBiG,EAAcpD,GAC/B,IACoEqD,EADpEC,E,goBAAAC,CACuB/G,OAAOgH,oBAAoBJ,EAAQtG,YAAU,IAApE,IAAAwG,EAAAG,MAAAJ,EAAAC,EAAAI,KAAAC,MAAsE,KAA3DC,EAAQP,EAAAlG,MAEF,gBAAbyG,GAA2C,gBAAbA,IAE9B5D,EAAS4D,GAAY5D,EAAS4D,GAAUC,KAAK7D,GAEjD,CAAC,OAAA8D,GAAAR,EAAAH,EAAAW,EAAA,SAAAR,EAAAP,GAAA,CACH,I,4FAAC,CAxfY,GAwfZpF,EAxfGP,EAAS,OA4iBC,WAAO,GAevB,Q,m0BC9jBA,IA+QA,EA5QsB,WAkBnB,O,EAXD,SAAA2G,EAAA1G,GAA8D,IAAA2G,EAAA,KAAhDvG,EAAQJ,EAARI,SAAUH,EAAUD,EAAVC,Y,4FAAUI,CAAA,KAAAqG,GAAApG,EAAA,0BAAAA,EAAA,wBAAAA,EAAA,qBAyPlB,SAACwF,GACf,OACEA,EAAEc,QACCd,EAAEe,SACFf,EAAEgB,UACF,CAAC,MAAO,WAAY,MAAO,UAAW,YAAa,YAAa,cAAcpG,SAC/EoF,EAAEiB,MAAQjB,EAAE7G,KAAO0H,EAAKK,aAAalB,aAAC,EAADA,EAAGmB,SAG9C,GA9PEnI,KAAKsB,SAAWA,EAChBtB,KAAKmB,WAAaA,EAKlBF,EAAUU,YAAYiG,EAAkB5H,KAC1C,E,EAAC,EAAAG,IAAA,yBAAAa,MAED,SAAuBgG,GACrB,IAAMpE,EAAU5C,KAAKmB,aAElByB,EAAQwF,yCAA2CpI,KAAKqI,cAAcrB,KACvEA,EAAEsB,iBACFtB,EAAEuB,4BAGJ,IAAMC,EAAgBxI,KAAKyI,2BAA2BzB,GAEtDhH,KAAKsB,SAAS,SAACuC,GACb,IAII6E,EACAC,EALEC,EAAwB/E,EAASgF,iBAAiBL,GAClDM,EAAwBjF,EAASgF,iBAAiB,IAAD3G,OACjDsG,EAAa,MAKnB,GAAII,EACFF,EAAYE,EACZD,EAAaH,MACR,KAAIM,EAIT,OAHAJ,EAAYI,EACZH,EAAa,IAAHzG,OAAOsG,EAAa,IAGhC,CAEA,IAakEO,EAAAC,EAYAC,EAAAC,EAzB5DC,EAAmB,SAACC,GACxBA,EAAcC,MAAMC,WAClB1G,EAAQ2G,kCAAoC,UAC5CH,EAAcC,MAAMG,MACpB5G,EAAQ6G,oCAAsC,OAClD,EAEA,GAAIf,EACF,GAAGxC,MAAMwD,QAAQhB,IAIf,GAHAA,EAAUiB,QAAQ,SAAAP,GAAa,OAAID,EAAiBC,EAAc,GAG9DxG,EAAQgH,+BACV,GAAIhH,EAAQiH,+CACE,QAAZd,EAAAL,EAAU,UAAE,IAAAK,GAAe,QAAfC,EAAZD,EAAce,qBAAa,IAAAd,GAA3BA,EAAAnI,KAAAkI,EAA8B/B,QACzB,GAAIpE,EAAQmH,uCAAwC,KAAAC,EAC7C,QAAZA,EAAAtB,EAAU,UAAE,IAAAsB,GAAZA,EAAcC,OAChB,MACEpG,EAASqG,oBAAoBvB,EAAY3B,QAI7CmC,EAAiBT,GAEb9F,EAAQgH,iCACNhH,EAAQiH,+CACD,QAATZ,EAAAP,SAAS,IAAAO,GAAe,QAAfC,EAATD,EAAWa,qBAAa,IAAAZ,GAAxBA,EAAArI,KAAAoI,EAA2BjC,GAClBpE,EAAQmH,uCACjBrB,EAAUuB,QAEVpG,EAASqG,oBAAoBvB,EAAY3B,GAKnD,EACF,GAAC,CAAA7G,IAAA,uBAAAa,MAED,SAAqBgG,GACnB,IAAMpE,EAAU5C,KAAKmB,aAElByB,EAAQwF,yCAA2CpI,KAAKqI,cAAcrB,KACvEA,EAAEsB,iBACFtB,EAAEuB,4BAGJ,IAAMC,EAAgBxI,KAAKyI,2BAA2BzB,GAEtDhH,KAAKsB,SAAS,SAACuC,GACb,IAegEsG,EAAAC,EAMAC,EArB1D3B,EACJ7E,EAASgF,iBAAiBL,IAC1B3E,EAASgF,iBAAiB,IAAD3G,OAAKsG,EAAa,MAEvCW,EAAmB,SAACC,GACrBA,EAAckB,iBACflB,EAAckB,gBAAgB,QAElC,EAEI5B,IACCxC,MAAMwD,QAAQhB,IACfA,EAAUiB,QAAQ,SAAAP,GAAa,OAAID,EAAiBC,EAAc,GAG9DxG,EAAQiH,iDACE,QAAZM,EAAAzB,EAAU,UAAE,IAAAyB,GAAa,QAAbC,EAAZD,EAAcI,mBAAW,IAAAH,GAAzBA,EAAAvJ,KAAAsJ,EAA4BnD,MAG9BmC,EAAiBT,GAEb9F,EAAQiH,iDACVnB,SAAsB,QAAb2B,EAAT3B,EAAW6B,mBAAW,IAAAF,GAAtBA,EAAAxJ,KAAA6H,EAAyB1B,KAIjC,EACF,GAEA,CAAA7G,IAAA,6BAAAa,MAIA,SAA2BgG,GAAkB,IAAAwD,EACvC1H,EAAS,GACP2H,EAAQzD,EAAEiB,MAAQjB,EAAE7G,KAAOH,KAAKkI,aAAalB,aAAC,EAADA,EAAGmB,SAgBtD,OALErF,EARA2H,SAAAA,EAAO7I,SAAS,WAChB6I,SAAAA,EAAO7I,SAAS,UAChB6I,SAAAA,EAAO7I,SAAS,UAChB6I,SAAAA,EAAO7I,SAAS,cAChB6I,SAAAA,EAAO7I,SAAS,YAChB6I,SAAAA,EAAO7I,SAAS,QAChB6I,SAAAA,EAAO7I,SAAS,QAEPoF,EAAEiB,MAAQ,GAEVjB,EAAE7G,KAAOH,KAAKkI,aAAalB,aAAC,EAADA,EAAGmB,UAAY,IAGvCzG,OAAS,EAAU,QAAT8I,EAAG1H,SAAM,IAAA0H,OAAA,EAANA,EAAQ/E,cAAgB3C,CACrD,GAEA,CAAA3C,IAAA,eAAAa,MAGA,SAAamH,GACX,MAAO,CACL,EAAG,YACH,EAAG,MACH,GAAI,QACJ,GAAI,QACJ,GAAI,OACJ,GAAI,MACJ,GAAI,QACJ,GAAI,WACJ,GAAI,MACJ,GAAI,QACJ,GAAI,SACJ,GAAI,WACJ,GAAI,MACJ,GAAI,OACJ,GAAI,YACJ,GAAI,UACJ,GAAI,aACJ,GAAI,YACJ,GAAI,SACJ,GAAI,SACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,OACJ,GAAI,UACJ,GAAI,UACJ,GAAI,UACJ,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,iBACL,IAAK,YACL,IAAK,iBACL,IAAK,gBACL,IAAK,eACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,MACL,IAAK,MACL,IAAK,MACL,IAAK,UACL,IAAK,aACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,KACL,IAAK,IACL,IAAK,KACLA,IAAY,EAChB,I,4FAAC,CA9PmB,G,m0BCJwB,IAQxCuC,EAAY,WAYf,O,EALD,SAAAA,EAAAxJ,GAAwD,IAA1CyJ,EAASzJ,EAATyJ,UAAW/H,EAAO1B,EAAP0B,S,4FAAOrB,CAAA,KAAAmJ,GAAAlJ,EAAA,yBAAAA,EAAA,uBAAAA,EAAA,mCAAAA,EAAA,iBAHpB,GAACA,EAAA,wBAIXxB,KAAK2K,UAAYA,EACjB3K,KAAK4C,QAAUA,EACf3B,EAAUU,YAAY+I,EAAc1K,MACpCA,KAAK4K,SAAW5K,KAAK2K,UAAUxJ,aAAa0J,0BAA4B,CAC1E,E,EAAC,EAAA1K,IAAA,UAAAa,MAED,WACMhB,KAAK8K,sBACP9K,KAAK8K,oBAAoBC,SACzB/K,KAAKgL,UAAY,EAErB,GAAC,CAAA7K,IAAA,OAAAa,MAED,SAAIiK,GAI6B,IAAApD,EAAA,KAH/BqD,EAAcD,EAAdC,eACAC,EAAaF,EAAbE,cACAC,EAAQH,EAARG,SAEA,GAAKF,GAAmBA,EAAexJ,OAAvC,CAIA,IAAM2J,EAAqBrL,KAAK2K,UAAUW,WACxCJ,EAAevF,MAAM,KACrB3F,KAAK4K,UAGP5K,KAAKuL,WAAW,CACdF,mBAAAA,EACAF,cAAAA,EACAH,UAAWhL,KAAKgL,UAChBQ,QAASH,EAAmB3J,OAC5B+J,eAAgB,SAACC,EAA2B1E,GAC1CoE,EAASM,EAAmB1E,GAC5Ba,EAAK8D,SACP,GAfF,CAiBF,GAAC,CAAAxL,IAAA,aAAAa,MAED,SAAU4K,GAMmB,IAAAC,EAAAC,EAAA,KAL3BT,EAAkBO,EAAlBP,mBACAF,EAAaS,EAAbT,cACAH,EAASY,EAATZ,UACAQ,EAAOI,EAAPJ,QACAC,EAAcG,EAAdH,eAGwB,QAAxBI,EAAA7L,KAAK8K,2BAAmB,IAAAe,GAAxBA,EAA0Bd,SAG1B/K,KAAK8K,oBAAsBiB,SAASC,cAAc,OAClDhM,KAAK8K,oBAAoBmB,UAAY,mBAGrC,IAAMC,EAAyBH,SAASC,cAAc,MACtDE,EAAuBD,UAAY,wBAGnCZ,EAAmBL,GAAWrB,QAAQ,SAACwC,GAAsB,IAAAC,EACrDC,EAAyBN,SAASC,cAAc,MAChDM,EAAgB,WACpB,IAAMC,EAAa,IAAKT,EAAKlJ,QAAQ4J,eAAiBC,WAAaC,YAAY,SAI/E,OAHArM,OAAOC,eAAeiM,EAAY,SAAU,CAC1CvL,MAAOqL,IAEFE,CACT,EAEAF,EAAuBJ,UAAY,6BACnCI,EAAuBM,WAAgC,QAApBP,EAAAN,EAAKlJ,QAAQT,eAAO,IAAAiK,OAAA,EAApBA,EAAuBD,KAAsBA,EAE7EL,EAAKlJ,QAAQ4J,eACdH,EAAuBO,aAAe,SAAC5F,GAAM,OAC3CyE,EAAeU,EAAmBnF,GAAKsF,IAAgB,EAEzDD,EAAuBQ,QAAU,eAAC7F,EAAC5E,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAGkK,IAAe,OACnDb,EAAeU,EAAmBnF,EAAE,EAIxCkF,EAAuBY,YAAYT,EACrC,GAGA,IAAMU,EAAyB/B,EAAY,EACrCgC,EAAiBjB,SAASC,cAAc,OAC9CgB,EAAeC,UAAUC,IAAI,yBAC7BH,GACEC,EAAeC,UAAUC,IAAI,+BAE/B,IAAMC,EAA4B,WAC3BJ,GACLjB,EAAKP,WAAW,CACdF,mBAAAA,EACAF,cAAAA,EACAH,UAAWA,EAAY,EACvBQ,QAAAA,EACAC,eAAAA,GAEJ,EAEGzL,KAAK4C,QAAQ4J,eACdQ,EAAeJ,aAAeO,EAE9BH,EAAeH,QAAUM,EAG3BnN,KAAK8K,oBAAoBgC,YAAYE,GAGrChN,KAAK8K,oBAAoBgC,YAAYZ,GAGrC,IAAMkB,EAAyBpC,EAAYQ,EAAU,EAC/C6B,EAAiBtB,SAASC,cAAc,OAC9CqB,EAAeJ,UAAUC,IAAI,yBAC7BE,GACEC,EAAeJ,UAAUC,IAAI,+BAE/B,IAAMI,EAA4B,WAC3BF,GACLtB,EAAKP,WAAW,CACdF,mBAAAA,EACAF,cAAAA,EACAH,UAAWA,EAAY,EACvBQ,QAAAA,EACAC,eAAAA,GAEJ,EAEGzL,KAAK4C,QAAQ4J,eACda,EAAeT,aAAeU,EAE9BD,EAAeR,QAAUS,EAG3BtN,KAAK8K,oBAAoBgC,YAAYO,GAGrClC,EAAcoC,QAAQvN,KAAK8K,oBAC7B,I,4FAAC,CApJe,GAuJlB,I,6vDClJA,IAOM0C,EAAc,WA0NjB,O,EA5LD,SAAAA,EACEC,EACAC,GACA,IAAA7F,EAAA,KACA,G,4FADAtG,CAAA,KAAAiM,GAAAhM,EAAA,qBAAAA,EAAA,uBAAAA,EAAA,yBAAAA,EAAA,6BAAAA,EAAA,gCAAAA,EAAA,2BAAAA,EAAA,qCAAAA,EAAA,gCAAAA,EAAA,8BAAAA,EAAA,mCAAAA,EAAA,oCAAAA,EAAA,qCAAAA,EAAA,uCAAAA,EAAA,gCAAAA,EAAA,uBAAAA,EAAA,iCAAAA,EAAA,sCAAAA,EAAA,2BAAAA,EAAA,2BAAAA,EAAA,2BAAAA,EAAA,4BAAAA,EAAA,+BAAAA,EAAA,mBAVY,WAASA,EAAA,0BAC6C,MAoMpEA,EAAA,oBAGe,SACbiM,EACAC,GAMA,IAAIC,EACAC,EACAhL,EAMJ,GAAiC,iBAAtB6K,EACTE,EAAmBF,EAAkB9H,MAAM,KAAKtB,KAAK,IACrDuJ,EAAc7B,SAAS8B,cAAc,IAAD3L,OAC9ByL,IAEN/K,EAAU8K,OAML,GAAID,aAA6BK,eAAgB,CAItD,IAAKL,EAAkBxB,UAErB,MADAzI,QAAQuK,KAAK,0DACP,IAAIC,MAAM,4BAGlBL,EAAmBF,EAAkBxB,UAAUtG,MAAM,KAAK,GAC1DiI,EAAcH,EACd7K,EAAU8K,CAKZ,MACEC,EAAmB,kBACnBC,EAAc7B,SAAS8B,cAAc,IAAD3L,OAC9ByL,IAEN/K,EAAU6K,EAGZ,MAAO,CACLE,iBAAAA,EACAC,YAAAA,EACAhL,QAAAA,EAEJ,GAEApB,EAAA,kBAGa,kBAAuBqG,EAAKjF,OAAO,GAAApB,EAAA,wBAC7B,kBAAqBqG,EAAK9D,aAAa,GAAAvC,EAAA,2BACpC,kBAAqBqG,EAAKoG,gBAAgB,GA6wChEzM,EAAA,sBAGiB,SAAC0M,EAAcC,GACzBtG,EAAKuG,QAAQF,KAAOrG,EAAKuG,QAAQF,GAAQ,CAAC,GAE/CC,EAAatG,EAAKuG,QAAQF,GAC5B,GA2HA1M,EAAA,8BAGyB,WAA8B,QAAA6M,EAAAjM,UAAAV,OAA1B4M,EAAc,IAAApI,MAAAmI,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAdD,EAAcC,GAAAnM,UAAAmM,GAKzC,MAJwB,CAAC1G,EAAK8F,kBAAgBzL,OAAKoM,GAAgBE,OACjE,SAACC,GAAQ,QAAOA,CAAQ,GAGHpK,KAAK,IAC9B,GAppDwB,oBAAXgB,OAAX,CAEA,IAAAqJ,EAII1O,KAAK2O,aAAalB,EAAmBC,GAHvCC,EAAgBe,EAAhBf,iBACAC,EAAWc,EAAXd,YAAWgB,EAAAF,EACX9L,QAAAA,OAAO,IAAAgM,EAAG,CAAC,EAACA,EAMd5O,KAAK2K,UAAY,IAAI1J,EAAU,CAC7BE,WAAYnB,KAAKmB,WACjBC,iBAAkBpB,KAAKoB,iBACvBC,oBAAqBrB,KAAKqB,oBAC1BC,SAAUtB,KAAKsB,WAMjBtB,KAAK+D,cAAgB,KAKrB/D,KAAKiO,iBAAmB,KAKxBjO,KAAK4N,YAAcA,EAuDnB5N,KAAK4C,Q,2VAAOiM,CAAA,CACVC,WAAY,UACZC,MAAO,mBACP/J,UAAW,UACXgK,yBAAyB,EACzBC,wBAAwB,EACxBC,kBAAmB,CAAC,GACjBtM,GAML5C,KAAKmP,sBAAwB,GAK7BlO,EAAUU,YAAY6L,EAAgBxN,MAgBtC,IAAAoP,EAAyCpP,KAAK4C,QAAtCoC,UAAAA,OAAS,IAAAoK,EAAGpP,KAAKqP,YAAWD,EAqDpC,GApDApP,KAAKwC,MAAQ,CAAC,EACdxC,KAAKwC,MAAMwC,GAAa,GAKxBhF,KAAK2N,iBAAmBA,EAKxB3N,KAAKsP,eAAiB,CAAC,EAMjBjK,OAA6C,0BAChDA,OAA6C,wBAAI,CAAC,GAErDrF,KAAKuP,oBAAsBvP,KAAK2K,UAAU6E,UAAUxP,KAAK2N,kBACxDtI,OAA6C,wBAAErF,KAAKuP,qBAAuBvP,KAK5EA,KAAKyP,qBAAwBpK,OAA6C,wBAC1ErF,KAAK0P,sBAAwBrP,OAAOsP,KAAMtK,OAA6C,yBACvFrF,KAAK4P,wBACH5P,KAAK0P,sBAAsB,KAAO1P,KAAKuP,oBAKzCvP,KAAK6P,iBAAmB,IAAIjI,EAAiB,CAC3CtG,SAAUtB,KAAKsB,SACfH,WAAYnB,KAAKmB,aAMnBnB,KAAK8P,aAAe9P,KAAK4C,QAAQqM,uBAC7B,IAAIvE,EAAa,CACfC,UAAW3K,KAAK2K,UAChB/H,QAAS5C,KAAK4C,UAEhB,MAKA5C,KAAK4N,YAGP,MADApK,QAAQuK,KAAK,KAAD7L,OAAMyL,EAAgB,gCAC5B,IAAIK,MAAM,sBAHIhO,KAAK+P,SAS3B/P,KAAKoO,QAAU,CAAC,EAChBpO,KAAKgQ,aAvLoC,CAwL3C,E,EAAC,EAAA7P,IAAA,mBAAAa,MA2ED,SAAiBkD,GAAuD,IAA9B+L,EAAW7N,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG8B,EACtDlE,KAAK+D,cAAgBG,EACrBlE,KAAKiO,iBAAmBgC,CAC1B,GAEA,CAAA9P,IAAA,qBAAAa,MAIA,SACEwB,GAC0E,IAAAsJ,EAAA,KAC1EoE,EAGIlQ,KAAK4C,QAFWuN,EAAmBD,EAArCE,iBACAC,EAAkCH,EAAlCG,mCAGF,IAAKF,GAAsD,WAA/BhL,EAAOgL,GACjC,MAAO,CAAC,EAGV,IAAMC,EAAmB/P,OAAOsP,KAAKQ,GAAqB3B,OACxD,SAAC8B,GACC,IAAMC,EACJ/N,EAAMiC,UAAU,EAAGqH,EAAKzK,uBAAyB,IAAMmB,EACnDgO,EAAS,IAAIC,OAAO,GAADvO,OACpB4J,EAAKnB,UAAU+F,YAAYJ,GAAgB,KAC9CD,EAAqC,IAAM,MAG7C,QADapK,EAAOsK,EAAYI,SAASH,IACxB9O,MACnB,GAGF,GAAI0O,EAAiB1O,OAAS,EAAG,CAC/B,IAAMkP,EAAeR,EAAiBS,KACpC,SAACC,EAAGC,GAAC,OAAKA,EAAErP,OAASoP,EAAEpP,MAAM,GAC7B,GACF,MAAO,CACLkP,aAAAA,EACA1F,eAAgBiF,EAAoBS,GAExC,CAAO,GAAIR,EAAiB1O,OAAQ,CAClC,IAAMkP,EAAeR,EAAiB,GACtC,MAAO,CACLQ,aAAAA,EACA1F,eAAgBiF,EAAoBS,GAExC,CACE,MAAO,CAAC,CAEZ,GAEA,CAAAzQ,IAAA,oBAAAa,MAKA,SACE4P,EACA1F,EACAC,GACM,IAAA6F,EAAA,KACFhR,KAAK8P,cACP9P,KAAK8P,aAAamB,KAAK,CACrB/F,eAAAA,EACAC,cAAAA,EACAC,SAAU,SAACM,EAA2B1E,GACpC,IAAAkK,EAIIF,EAAKpO,QAHPyN,EAAkCa,EAAlCb,mCACAc,EAA6BD,EAA7BC,8BACAC,EAA8BF,EAA9BE,+BAGEC,EAAe3F,EAEfyF,IAIFE,EAAe3F,EAAkB4F,UAAU,QAMC,mBAAnCN,EAAKpO,QAAQ2O,mBACtBP,EAAKpO,QAAQ2O,kBAAkBP,GAGjC,IAAMjM,EAAeiM,EAAKQ,SAASR,EAAKpO,QAAQoC,WAAW,GACrDyM,EAAuBT,EAAK3P,uBAAyB,EACrDkP,EACJxL,EAAaN,UAAU,EAAGgN,GAAwB,IAClD1M,EAEIyL,EAAS,IAAIC,OAAO,GAADvO,OACpB8O,EAAKrG,UAAU+F,YAAYE,GAAa,KAC3CP,EAAqC,IAAM,MAEvCqB,EAAiBnB,EAAYvO,QACjCwO,EACAa,GAEIM,EAAW5M,EAAa/C,QAAQuO,EAAamB,GAE7CE,EAAoBF,EAAehQ,OAAS6O,EAAY7O,OAC1DmQ,GACDJ,GAAwB1M,EAAarD,QAAUkQ,EAE9CC,EAAmB,IAAGA,EAAmB,GAE7Cb,EAAKc,SAASH,EAAUX,EAAKpO,QAAQoC,WAAW,GAChDgM,EAAKlN,iBAAiB+N,GAOlBT,GAAqE,mBAA5BJ,EAAKpO,QAAQmP,YACxDf,EAAKpO,QAAQmP,WAAWrG,EAAmB1E,GAER,mBAA1BgK,EAAKpO,QAAQoP,UACtBhB,EAAKpO,QAAQoP,SACXhB,EAAKQ,SAASR,EAAKpO,QAAQoC,WAAW,GACtCgC,GAMoC,mBAA7BgK,EAAKpO,QAAQqP,aACtBjB,EAAKpO,QAAQqP,YAAYjB,EAAKkB,eAAgBlL,EAClD,GAGN,GAEA,CAAA7G,IAAA,sBAAAa,MAIA,SAAoBS,EAAgBuF,GAClC,IAAAmL,EAAgDnS,KAAK4C,QAAOwP,EAAAD,EAApDnN,UAAAA,OAAS,IAAAoN,EAAGpS,KAAKqP,YAAW+C,EAAE7O,EAAK4O,EAAL5O,MAItC,GAAe,SAAX9B,EAAJ,CAKKzB,KAAKwC,MAAMwC,KAAYhF,KAAKwC,MAAMwC,GAAa,IAKN,mBAAnChF,KAAK4C,QAAQ2O,mBACtBvR,KAAK4C,QAAQ2O,kBAAkBvR,MAMjC,IAAM6E,EAAe7E,KAAK2K,UAAU0H,gBAClC5Q,EACAzB,KAAKwC,MAAMwC,GACXhF,KAAK+D,cACL/D,KAAKiO,kBAMP,GAAIjO,KAAK2K,UAAU2H,iBAAiB7Q,IAAWzB,KAAKuS,oBAEhDvS,KAAKwC,MAAMwC,IACXhF,KAAKwC,MAAMwC,KAAeH,GACH,IAAvB7E,KAAK+D,eACL/D,KAAKiO,mBAAqBpJ,EAAanD,OAQvC,OALA1B,KAAK8R,SAAS,GAAI9R,KAAK4C,QAAQoC,WAAW,GAC1ChF,KAAK8D,iBAAiB,GACtB9D,KAAKuS,mBAAmBvR,MAAQ,GAChChB,KAAKuS,mBAAmBC,kBAAkB,EAAG,QAC7CxS,KAAKkK,oBAAoBzI,EAAQuF,GAWrC,GAHuC,mBAA5BhH,KAAK4C,QAAQmP,YACtB/R,KAAK4C,QAAQmP,WAAWtQ,EAAQuF,GAIhChH,KAAKwC,MAAMwC,KAAeH,KAGxB7E,KAAK4C,QAAQ6P,cAEZzS,KAAK4C,QAAQ6P,cAAgBzS,KAAK0S,oBAAoB7N,IACzD,CAIA,GACE7E,KAAK4C,QAAQkC,WACb9E,KAAK2K,UAAUgI,gBAAgB3S,KAAKwC,MAAOqC,GAE3C,OAMF,IAAM+N,EAAgB5S,KAAK2K,UAAU0H,gBACnC5Q,EACAzB,KAAKwC,MAAMwC,GACXhF,KAAK+D,cACL/D,KAAKiO,kBACL,GAqCF,GAlCAjO,KAAK8R,SAASc,EAAe5S,KAAK4C,QAAQoC,WAAW,GAEjDzB,GAAOC,QAAQC,IAAI,iBAAkBzD,KAAKkS,gBAE1ClS,KAAK4C,QAAQW,OACfC,QAAQC,IACN,aACAzD,KAAKoB,mBACLpB,KAAKqB,sBAAqB,IAAAa,OACtBlC,KAAK2N,iBAAgB,KACzB3G,aAAC,EAADA,EAAG6L,MAOH7S,KAAK4C,QAAQkQ,oBAAoB9S,KAAK8S,qBAKL,mBAA1B9S,KAAK4C,QAAQoP,UACtBhS,KAAK4C,QAAQoP,SAAShS,KAAKwR,SAASxR,KAAK4C,QAAQoC,WAAW,GAAOgC,GAK7B,mBAA7BhH,KAAK4C,QAAQqP,aACtBjS,KAAK4C,QAAQqP,YAAYjS,KAAKkS,eAAgBlL,GAK5CA,SAAAA,EAAG+L,QAAU/S,KAAK4C,QAAQqM,uBAAwB,CACpD,IASO+D,EATPC,EACEjT,KAAKkT,mBAAmBrO,GADlB+L,EAAYqC,EAAZrC,aAAc1F,EAAc+H,EAAd/H,eAGlB0F,GAAgB1F,EAClBlL,KAAKmT,kBACHvC,EACA1F,EACAlL,KAAK4N,aAGU,QAAjBoF,EAAAhT,KAAK8P,oBAAY,IAAAkD,GAAjBA,EAAmBrH,SAEvB,CACF,CAMG3L,KAAKiO,kBAAoBjO,KAAK+D,gBAAkB/D,KAAKiO,mBACtDjO,KAAK8D,iBAAiB9D,KAAKiO,iBAAkBjO,KAAKiO,kBAE/CjO,KAAKuS,oBACNvS,KAAKuS,mBAAmBC,kBAAkBxS,KAAKiO,iBAAkBjO,KAAKiO,kBAGrEjO,KAAK4C,QAAQW,OACdC,QAAQC,IAAI,yBAA0BzD,KAAK+D,gBAI3CR,GACFC,QAAQC,IAAI,eAAgBhC,EAnJD,CAqJ/B,GAEA,CAAAtB,IAAA,eAAAa,MAGA,WACE,OAAOhB,KAAKoT,WACd,GAEA,CAAAjT,IAAA,eAAAa,MAGA,SAAaA,GACPhB,KAAK4C,QAAQkQ,mBACf9S,KAAKsB,SAAS,SAACuC,GACbA,EAASuP,YAAcpS,CACzB,GAEAhB,KAAKoT,YAAcpS,CAEvB,GAKA,CAAAb,IAAA,wBAAAa,MACA,SAAsBS,EAAgBuF,GAA+B,IAAAqM,EAAA,KAC/DrM,IAIEhH,KAAK4C,QAAQoM,yBAAyBhI,EAAEsB,iBACxCtI,KAAK4C,QAAQ0Q,0BAA0BtM,EAAEuM,kBAK7CvM,EAAE+L,OAAO9F,UAAUC,IAAIlN,KAAKwT,oBAG1BxT,KAAKyT,wBAAwBC,aAAa1T,KAAKyT,wBAC/CzT,KAAK2T,aAAaD,aAAa1T,KAAK2T,aAKxC3T,KAAK4T,cAAa,GAKb5T,KAAK4C,QAAQiR,oBAChB7T,KAAK2T,YAActO,OAAOyO,WAAW,YAEhCT,EAAKU,kBAEDtS,EAAOG,SAAS,OAASH,EAAOG,SAAS,MAC/B,aAAXH,GACW,gBAAXA,GACW,WAAXA,GACW,YAAXA,GACW,UAAXA,IACO,iBAAXA,GACW,gBAAXA,GACW,cAAXA,GACW,gBAAXA,KAEI4R,EAAKzQ,QAAQW,OAAOC,QAAQC,IAAI,eAAgBhC,GAEpD4R,EAAKW,iBAAiBvS,IAExBiS,aAAaL,EAAKM,YACpB,EAAG,KAEP,GAEA,CAAAxT,IAAA,sBAAAa,MAGA,SAAoBS,EAAiBuF,GAAgC,IAAAiN,EAAA,KAC/DjN,IAIEhH,KAAK4C,QAAQsR,uBAAyBlN,EAAEsB,gBAC1CtB,EAAEsB,iBACAtI,KAAK4C,QAAQuR,wBAA0BnN,EAAEuM,iBAC3CvM,EAAEuM,oBAIFvM,EAAE+L,SAAW/S,KAAK4N,aACjB5G,EAAE+L,QAAU/S,KAAK4N,YAAYwG,SAASpN,EAAE+L,SACxC/S,KAAK8P,cACJ9P,KAAK8P,aAAahF,sBACjB9D,EAAE+L,SAAW/S,KAAK8P,aAAahF,qBAC7B9D,EAAE+L,QACD/S,KAAK8P,aAAahF,oBAAoBsJ,SAASpN,EAAE+L,WAKtC/S,KAAK8P,cACtB9P,KAAK8P,aAAanE,WAOtB3L,KAAKqU,eAAe,SAACjL,GACnBA,EAAc6D,UAAUlC,OAAOkJ,EAAKT,kBACtC,GAEAxT,KAAK4T,cAAa,GACd5T,KAAKyT,wBAAwBC,aAAa1T,KAAKyT,wBAK/ChS,GAAgD,mBAA/BzB,KAAK4C,QAAQ0R,eAChCtU,KAAK4C,QAAQ0R,cAAc7S,EAAQuF,EACvC,GAEA,CAAA7G,IAAA,mCAAAa,MAGA,SAAiCgG,GAI3BhH,KAAK4C,QAAQoM,yBAAyBhI,EAAEsB,gBAC9C,GAKA,CAAAnI,IAAA,mBAAAa,MACA,SAAiBS,GAAsB,IAAA8S,EAAA,KACjCvU,KAAKyT,wBAAwBC,aAAa1T,KAAKyT,wBAKnDzT,KAAKyT,uBAAyBpO,OAAOyO,WAAW,WAC1CS,EAAKR,gBACPQ,EAAKrK,oBAAoBzI,GACzB8S,EAAKP,iBAAiBvS,IAEtBiS,aAAaa,EAAKd,uBAEtB,EAAG,IACL,GAEA,CAAAtT,IAAA,qBAAAa,MAGA,WAA2B,IAAAwT,EAAA,KACzBxU,KAAKsB,SAAS,SAACuC,GACbA,EAAS4Q,aAAaD,EAAKhS,OAC3BqB,EAASC,iBAAiB0Q,EAAKzQ,cAAeyQ,EAAKvG,iBACrD,EACF,GAEA,CAAA9N,IAAA,aAAAa,MAIA,WAEQ,IADNgE,EAAiB5C,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAGpC,KAAK4C,QAAQoC,WAAahF,KAAKqP,YAEnDrP,KAAKwC,MAAMwC,GAAa,GAKxBhF,KAAK8D,iBAAiB,GAKlB9D,KAAK4C,QAAQkQ,oBAAoB9S,KAAK8S,oBAC5C,GAEA,CAAA3S,IAAA,WAAAa,MAIA,WAGU,IAFRgE,EAAiB5C,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAGpC,KAAK4C,QAAQoC,WAAahF,KAAKqP,YACnDqF,EAAQtS,UAAAV,OAAA,QAAAW,IAAAD,UAAA,IAAAA,UAAA,GAOR,OAFIpC,KAAK4C,QAAQkQ,qBAAuB4B,GAAU1U,KAAK8S,qBAEnD9S,KAAK4C,QAAQ+R,IAMR,IAJwB3U,KAAKwC,MAAMwC,GACvChD,QAAQ,IAAU,IAClBA,QAAQ,IAAU,IAEsB,IAEpChC,KAAKwC,MAAMwC,EAEtB,GAEA,CAAA7E,IAAA,eAAAa,MAGA,WAA8B,IAAA4T,EAAA,KACtB9R,EAAS,CAAC,EAOhB,OANmBzC,OAAOsP,KAAK3P,KAAKwC,OAEzBmH,QAAQ,SAAC3E,GAClBlC,EAAOkC,GAAa4P,EAAKpD,SAASxM,GAAW,EAC/C,GAEOlC,CACT,GAEA,CAAA3C,IAAA,WAAAa,MAKA,SACEwB,GAGM,IAFNwC,EAAiB5C,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAGpC,KAAK4C,QAAQoC,WAAahF,KAAKqP,YACnDqF,EAAkBtS,UAAAV,OAAA,EAAAU,UAAA,QAAAC,EAElBrC,KAAKwC,MAAMwC,GAAaxC,GAKnBkS,GAAY1U,KAAK4C,QAAQkQ,oBAAoB9S,KAAK8S,oBACzD,GAEA,CAAA3S,IAAA,eAAAa,MAIA,SAAa4D,GACX5E,KAAKwC,MAAQoC,CACf,GAEA,CAAAzE,IAAA,aAAAa,MAIA,WAA+B,IAApB4B,EAAOR,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EACdyS,EAAiB7U,KAAK6U,eAAejS,GAC3C5C,KAAK4C,QAAUvC,OAAOiC,OAAOtC,KAAK4C,QAASA,GAEvCiS,EAAenT,SACb1B,KAAK4C,QAAQW,OACfC,QAAQC,IAAI,iBAAkBoR,GAMhC7U,KAAK8U,aAAaD,GAKlB7U,KAAK+P,SAET,GAEA,CAAA5P,IAAA,iBAAAa,MAIA,SAAe+T,GAAgD,IAAAC,EAAA,KAC7D,OAAO3U,OAAOsP,KAAKoF,GAAYvG,OAC7B,SAACyG,GAAU,OACTC,KAAKC,UAAUJ,EAAWE,MAC1BC,KAAKC,UAAUH,EAAKpS,QAAQqS,GAAY,EAE9C,GAEA,CAAA9U,IAAA,eAAAa,MAIA,WAAkD,IAArC6T,EAAwBzS,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,GAIlCyS,EAAejT,SAAS,eAItB5B,KAAK8P,cACP9P,KAAK8P,aAAanE,WAQpBkJ,EAAejT,SAAS,6BACxBiT,EAAejT,SAAS,sBAKpB5B,KAAK8P,eACP9P,KAAK8P,aAAanE,UAClB3L,KAAK8P,aAAe,IAAIpF,EAAa,CACnCC,UAAW3K,KAAK2K,UAChB/H,QAAS5C,KAAK4C,UAItB,GAEA,CAAAzC,IAAA,YAAAa,MAIA,WACMhB,KAAKoV,iBACPpV,KAAKoV,gBAAgBrK,SAGvB/K,KAAK4N,YAAY3B,UAAYjM,KAAK2N,iBAClC3N,KAAK4N,YAAYyH,aAAa,kBAAmBrV,KAAKuP,qBACtDvP,KAAKsP,eAAiB,CAAC,CACzB,GAMA,CAAAnP,IAAA,WAAAa,MACA,SAASsU,GACP,IAAMjQ,OAA6C,wBAIjD,MAHA7B,QAAQuK,KAAK,sEAGP,IAAIC,MAAM,uBAGlB,OAAO3N,OAAOsP,KAAMtK,OAA6C,yBAAGsE,QAAQ,SAACxJ,GAC3EmV,EAAUjQ,OAA6C,wBAAElF,GAAMA,EACjE,EACF,GAEA,CAAAA,IAAA,iBAAAa,MAKA,SAAeuU,EAAiBtJ,GAAyB,IAAAuJ,EAAA,KAClDvJ,GAAcsJ,IAEnBA,EAAQ5P,MAAM,KAAKgE,QAAQ,SAAClI,GAC1BwK,EAAUtG,MAAM,KAAKgE,QAAQ,SAAC8L,GACvBD,EAAK5S,QAAQ8S,cAAaF,EAAK5S,QAAQ8S,YAAc,IAE1D,IAAIC,GAAiB,EAKrBH,EAAK5S,QAAQ8S,YAAYrP,IAAI,SAACqP,GAC5B,GAAIA,SAAAA,EAAW,MAAQ/P,MAAM,KAAK/D,SAAS6T,GAAgB,CACzDE,GAAiB,EAEjB,IAAMC,EAAmBF,EAAYH,QAAQ5P,MAAM,KAC9CiQ,EAAiBhU,SAASH,KAC7BkU,GAAiB,EACjBC,EAAiBC,KAAKpU,GACtBiU,EAAYH,QAAUK,EAAiBvR,KAAK,KAEhD,CACA,OAAOqR,CACT,GAKKC,GACHH,EAAK5S,QAAQ8S,YAAYG,KAAK,CAC5BC,MAAOL,EACPF,QAASA,GAGf,EACF,GAEAvV,KAAK+P,SACP,GAEA,CAAA5P,IAAA,oBAAAa,MAKA,SAAkBuU,EAAiBtJ,GAAyB,IAAA8J,EAAA,KAI1D,IAAKR,IAAYtJ,EAGf,OAFAjM,KAAK4C,QAAQ8S,YAAc,QAC3B1V,KAAK+P,SAQLwF,GACArP,MAAMwD,QAAQ1J,KAAK4C,QAAQ8S,cAC3B1V,KAAK4C,QAAQ8S,YAAYhU,SAEL6T,EAAQ5P,MAAM,KACtBgE,QAAQ,SAAClI,GAAW,IAAAuU,EAClB,QAAZA,EAAAD,EAAKnT,eAAO,IAAAoT,GAAa,QAAbA,EAAZA,EAAcN,mBAAW,IAAAM,GAAzBA,EAA2B3P,IAAI,SAACqP,EAAalP,GAK3C,GACGkP,GACCzJ,GACAA,EAAUrK,SAAS8T,EAAW,SAC/BzJ,EACD,KAAAgK,EAUOC,EATDC,EAAiC,QAAdF,EAAGP,SAAW,IAAAO,OAAA,EAAXA,EAAaV,QACtC5P,MAAM,KACN6I,OAAO,SAAC4H,GAAI,OAAKA,IAAS3U,CAAM,GAK/BiU,SAAeS,GAAAA,EAAqBzU,OACtCgU,EAAYH,QAAUY,EAAoB9R,KAAK,MAEvB,QAAxB6R,EAAAH,EAAKnT,QAAQ8S,mBAAW,IAAAQ,GAAxBA,EAA0BG,OAAO7P,EAAO,GACxCkP,EAAc,KAElB,CAEA,OAAOA,CACT,EACF,GAEA1V,KAAK+P,SAET,GAEA,CAAA5P,IAAA,mBAAAa,MAIA,SACES,GAEA,IAAIqB,EAEEwT,EAAYtW,KAAKsP,eAAe7N,GAStC,OARI6U,IAEAxT,EADEwT,EAAU5U,OAAS,EACZ4U,EAEAA,EAAU,IAIhBxT,CACT,GAEA,CAAA3C,IAAA,sBAAAa,MAIA,SAAoBuV,GAClB,IACI9D,EADE+D,EAAkBxW,KAAK4C,QAAQ6P,aAarC,IANEA,EADE+D,aAA2B/F,OACd+F,EAGbA,EAAgBxW,KAAK4C,QAAQoC,WAAahF,KAAKqP,eAG/BkH,EAAU,CAC5B,IAAME,EAAgBhE,EAAaiE,KAAKH,GAUxC,OARIvW,KAAK4C,QAAQW,OACfC,QAAQC,IAAI,kBAADvB,OACSuQ,EAAY,QAAAvQ,OAC5BuU,EAAgB,SAAW,kBAK1BA,CACT,CAIE,OAAO,CAEX,GAEA,CAAAtW,IAAA,oBAAAa,MAGA,WAIE,GAAIhB,KAAK4P,0BAA4B5P,KAAKyP,qBAAsB,CAC1DzP,KAAK4C,QAAQW,OACfC,QAAQC,IAAI,2BAADvB,OAA4BlC,KAAK2N,iBAAgB,MAG9D,IAAAgJ,EAA4D3W,KAAK4C,QAAzDwF,wCAAAA,OAAuC,IAAAuO,GAAQA,EAKvD5K,SAAS6K,iBAAiB,QAAS5W,KAAK6W,YAAazO,GACrD2D,SAAS6K,iBAAiB,UAAW5W,KAAK8W,cAAe1O,GACzD2D,SAAS6K,iBAAiB,UAAW5W,KAAK+W,eAC1ChL,SAAS6K,iBAAiB,WAAY5W,KAAKgX,gBAEvChX,KAAK4C,QAAQqU,8BACflL,SAAS6K,iBAAiB,kBAAmB5W,KAAKkX,uBAGpDnL,SAAS6K,iBAAiB,SAAU5W,KAAKmX,aAC3C,CACF,GAEA,CAAAhX,IAAA,cAAAa,MAGA,SAAYoW,GACVpX,KAAKqX,kBAAkBD,GAEnBpX,KAAK4C,QAAQ0U,2BACftX,KAAK6P,iBAAiB0H,qBAAqBH,EAE/C,GAEA,CAAAjX,IAAA,gBAAAa,MAGA,SAAcoW,GACRpX,KAAK4C,QAAQ0U,2BACftX,KAAK6P,iBAAiB2H,uBAAuBJ,EAEjD,GAEA,CAAAjX,IAAA,gBAAAa,MAGA,SAAcoW,GACZpX,KAAKqX,kBAAkBD,EACzB,GAKA,CAAAjX,IAAA,iBAAAa,MACA,SAAeoW,GACbpX,KAAKqX,kBAAkBD,EACzB,GAKA,CAAAjX,IAAA,eAAAa,MACA,SAAaoW,GACXpX,KAAKqX,kBAAkBD,EACzB,GAKA,CAAAjX,IAAA,wBAAAa,MACA,SAAsBoW,GAKjB9R,UAAUmS,UAAU7V,SAAS,YAGhC5B,KAAKqX,kBAAkBD,EACzB,GAEA,CAAAjX,IAAA,oBAAAa,MAGA,SAAkBoW,GAAmC,IAC/CM,EAD+CC,EAAA,KAE/CP,EAAMrE,OAAO6E,UACfF,EAAgBN,EAAMrE,OAAO6E,QAAQnS,eAGvCzF,KAAKsB,SAAS,SAACuC,GACb,IAAIgU,EACFT,EAAMrE,SAAWlP,EAAS+J,aACzBwJ,EAAMrE,QAAUlP,EAAS+J,YAAYwG,SAASgD,EAAMrE,QAYvD,GANI4E,EAAK/U,QAAQkQ,oBAAsB5M,MAAMwD,QAAQ0N,EAAMU,QACzDD,EAAaT,EAAMU,KAAKC,KAAK,SAAC3B,GAAiB,IAAA4B,EAAA,OAC7C5B,SAAkB,QAAd4B,EAAJ5B,EAAM6B,oBAAY,IAAAD,OAAA,EAAlBA,EAAAnX,KAAAuV,EAAqB,kBAAkB,KAKtB,aAAlBsB,GACoB,UAAlBA,GACC,CAAC,OAAQ,SAAU,MAAO,MAAO,YAAY9V,SAC3CwV,EAAMrE,OAAOF,SAElBhP,EAASjB,QAAQsV,wBAClB,CAKA,IAAIC,EAAiBf,EAAMrE,OAAOoF,eAC9BC,EAAehB,EAAMrE,OAAOqF,aAE7BvU,EAASjB,QAAQ+R,MAClBwD,EAAiBtU,EAAS8G,UAAU0N,aAAaF,EAAgBtU,EAAS2N,YAC1E4G,EAAevU,EAAS8G,UAAU0N,aAAaD,EAAcvU,EAAS2N,aAGxE3N,EAASC,iBAAiBqU,EAAgBC,GAK1CvU,EAAS0O,mBAAqB6E,EAAMrE,OAEhClP,EAASjB,QAAQW,OACnBC,QAAQC,IACN,aACAI,EAASzC,mBACTyC,EAASxC,sBACT+V,GAASA,EAAMrE,OAAO6E,QAAQnS,cAAa,IAAAvD,OACvC2B,EAAS8J,iBAAgB,KAC7ByJ,aAAK,EAALA,EAAOvE,KAGb,MACGhP,EAASjB,QAAQsV,yBAA4BL,GAC9B,qBAAhBT,aAAK,EAALA,EAAOvE,QAKPhP,EAASC,iBAAiB,MAK1BD,EAAS0O,mBAAqB,KAE1B1O,EAASjB,QAAQW,OACnBC,QAAQC,IAAI,gCAADvB,OACuBkV,aAAK,EAALA,EAAOvE,KAAI,WAC3CuE,GAIR,EACF,GAEA,CAAAjX,IAAA,iBAAAa,MAGA,SAAesX,GAAe,IAAAC,EAAA,KACvBD,GAELjY,OAAOsP,KAAK3P,KAAKsP,gBAAgB3F,QAAQ,SAAChB,GAAU,OAClD4P,EAAKjJ,eAAe3G,GAAYgB,QAAQ2O,EAAG,EAE/C,GAEA,CAAAnY,IAAA,UAAAa,MAGA,WACMhB,KAAK4C,QAAQW,OACfC,QAAQC,IAAI,wCAADvB,OAC+BlC,KAAKuP,sBAGjD,IAAAiJ,EAA4DxY,KAAK4C,QAAzDwF,wCAAAA,OAAuC,IAAAoQ,GAAQA,EAKvDzM,SAAS0M,oBAAoB,QAASzY,KAAK6W,YAAazO,GACxD2D,SAAS0M,oBAAoB,UAAWzY,KAAK8W,cAAe1O,GAC5D2D,SAAS0M,oBAAoB,UAAWzY,KAAK+W,eAC7ChL,SAAS0M,oBAAoB,WAAYzY,KAAKgX,gBAC9CjL,SAAS0M,oBAAoB,SAAUzY,KAAKmX,cAIxCnX,KAAK4C,QAAQqU,8BACflL,SAAS0M,oBAAoB,kBAAmBzY,KAAKkX,uBAGvDnL,SAASxB,YAAc,KACvBwB,SAAS2M,WAAa,KACtB3M,SAAS4M,cAAgB,KACzB5M,SAAS6M,UAAY,KAsBrB5Y,KAAKqU,eAjBgB,SAACjL,GAChBA,IACFA,EAAcU,cAAgB,KAC9BV,EAAcmB,YAAc,KAC5BnB,EAAcyP,gBAAkB,KAChCzP,EAAcwD,aAAe,KAC7BxD,EAAcsP,WAAa,KAC3BtP,EAAcuP,cAAgB,KAC9BvP,EAAcyD,QAAU,KACxBzD,EAAc0P,YAAc,KAC5B1P,EAAcwP,UAAY,KAE1BxP,EAAc2B,SACd3B,EAAgB,KAEpB,GAOApJ,KAAK4N,YAAY9D,cAAgB,KACjC9J,KAAK4N,YAAYhB,aAAe,KAChC5M,KAAK4N,YAAYkL,YAAc,KAK/B9Y,KAAK+Y,YAKD/Y,KAAK8P,eACP9P,KAAK8P,aAAanE,UAClB3L,KAAK8P,aAAe,MAMtB9P,KAAKuS,mBAAqB,KAK1BvS,KAAK4N,YAAYtD,gBAAgB,mBAKjCtK,KAAK4N,YAAYjB,UAAY,GAK5BtH,OAA6C,wBAAErF,KAAKuP,qBAAuB,YACpElK,OAA6C,wBAAErF,KAAKuP,qBAK5DvP,KAAKgZ,aAAc,CACrB,GAEA,CAAA7Y,IAAA,wBAAAa,MAGA,SAAsBS,GACpB,IAAMiU,EAAc1V,KAAK4C,QAAQ8S,YAC7BuD,EAA0B,GA0B9B,OAxBI/S,MAAMwD,QAAQgM,IAChBA,EAAY/L,QAAQ,SAACuP,GACnB,GACEA,GACAA,EAAQ,OACkB,iBAAnBA,EAAQ,OACfA,EAAS3D,SACmB,iBAArB2D,EAAS3D,QAChB,CACA,IAAM4D,EAAkBD,EAAQ,MAAOvT,MAAM,KACrBuT,EAAS3D,QAAQ5P,MAAM,KAE3B/D,SAASH,KAC3BwX,EAAgB,GAAH/W,OAAA+D,EAAOgT,GAAahT,EAAKkT,IAE1C,MACE3V,QAAQuK,KAAK,2DAEXmL,EAGN,GAGKD,CACT,GAEA,CAAA9Y,IAAA,yBAAAa,MAGA,SAAuBS,EAAgB6T,GACrC,IAAM8D,EAAmBpZ,KAAK4C,QAAQwW,iBAElClT,MAAMwD,QAAQ0P,IAChBA,EAAiBzP,QAAQ,SAAC0P,GAEtBA,EAAQC,WACqB,iBAAtBD,EAAQC,WACfD,EAAQrY,OACiB,iBAAlBqY,EAAQrY,OACfqY,EAAQ9D,SACmB,iBAApB8D,EAAQ9D,QAEQ8D,EAAQ9D,QAAQ5P,MAAM,KAE1B/D,SAASH,IAC1B6T,EAAS+D,EAAQC,UAAWD,EAAQrY,OAGtCwC,QAAQuK,KAAK,gEAEXsL,EAGN,EAEJ,GAAC,CAAAlZ,IAAA,wBAAAa,MAED,WAIEhB,KAAKuZ,yBAKLvZ,KAAKwZ,yBACP,GAKA,CAAArZ,IAAA,0BAAAa,MACA,WACEqE,OAAOoU,cAAgB,SAACrC,GAAgC,IAAAsC,EACtD,GAA0B,QAA1BA,EAAItC,EAAMrE,OAAO9F,iBAAS,IAAAyM,GAAtBA,EAAwBtF,SAAS,aAGnC,OAFAgD,EAAM9O,iBACN8O,EAAM7D,mBACC,CAEX,CACF,GAEA,CAAApT,IAAA,yBAAAa,MAGA,WACMhB,KAAK4C,QAAQ+W,qBACf3Z,KAAK4C,QAAQ4J,gBAAiB,EAE1BxM,KAAK4C,QAAQW,OACfC,QAAQC,IAAI,sEAKlB,GAEA,CAAAtD,IAAA,SAAAa,MAGA,WACMhB,KAAK4C,QAAQW,OACfC,QAAQC,IAAI,GAADvB,OAAIlC,KAAK2N,iBAAgB,iBAMtC3N,KAAK4Z,oBAE8B,mBAAxB5Z,KAAK4C,QAAQiX,QAAuB7Z,KAAK4C,QAAQiX,OAAO7Z,KACrE,GAEA,CAAAG,IAAA,oBAAAa,MAGA,WAIMhB,KAAK2K,UAAUmP,iBACjB9Z,KAAK+Z,wBAGuC,mBAAnC/Z,KAAK4C,QAAQoX,mBACtBha,KAAK4C,QAAQoX,kBAAkBha,MAM/BA,KAAK4P,yBACL5P,KAAK2K,UAAUsP,2BACdja,KAAK4C,QAAQ4J,iBACbxM,KAAK4C,QAAQsX,gBAEVla,KAAK4C,QAAQW,OACfC,QAAQC,IAAI,0DAOZzD,KAAK4C,QAAQ4J,gBACXxM,KAAK4C,QAAQW,OACfC,QAAQC,IACN,mEAIR,GAEA,CAAAtD,IAAA,eAAAa,MAGA,WAC2C,mBAA9BhB,KAAK4C,QAAQuX,cACtBna,KAAK4C,QAAQuX,aAAana,KAC9B,GAEA,CAAAG,IAAA,WAAAa,MAGA,WACuC,mBAA1BhB,KAAK4C,QAAQwX,UACtBpa,KAAK4C,QAAQwX,SAASpa,KAC1B,GAEA,CAAAG,IAAA,kBAAAa,MAGA,WAC8C,mBAAjChB,KAAK4C,QAAQyX,iBACtBra,KAAK4C,QAAQyX,gBAAgBra,KACjC,GAAC,CAAAG,IAAA,cAAAa,MAcD,WAAc,IAAAsZ,EAAA,KACRpU,MAAMwD,QAAQ1J,KAAK4C,QAAQwL,WAC7BpO,KAAK4C,QAAQwL,QAAQzE,QAAQ,SAAC4Q,GAC5B,IAAMC,EAAiBF,EAAK3P,UAAU8P,cAAcF,GAClD,IAAIA,EAAeD,GAAQC,EAAeD,GAE5CE,EAAeE,MAAQF,EAAeE,KAAKJ,EAC7C,GAEAta,KAAKmP,sBAAwB,iBAE7BnP,KAAK+P,SACL/P,KAAKqa,kBAET,GAEA,CAAAla,IAAA,gBAAAa,MAGA,SAAckN,EAAcxN,GAC1B,QAAKV,KAAKoO,QAAQF,IAEXlO,KAAKoO,QAAQF,GAAMxN,EAC5B,GAEA,CAAAP,IAAA,iBAAAa,MAGA,WACE,OAAOX,OAAOsP,KAAK3P,KAAKoO,QAC1B,GAEA,CAAAjO,IAAA,wBAAAa,MAGA,SACE2Z,EACAC,EACAC,EACAC,GACA,IAAAC,EAAA,KACMC,EAAc9U,MAAM+U,KAAKN,EAAOO,UAClCC,EAAkB,EAyEtB,OAvEIH,EAAYtZ,QACdmZ,EAAsBlR,QAAQ,SAACyR,EAAYC,GACzC,IAAMC,EAAWR,EAAoBO,GAMrC,KAAKC,GAAcA,EAAWF,GAC5B,OAAO,EAQT,IAAMG,EAAqBH,EAAaD,EAClCK,EAAmBF,EAAWH,EAK9BM,EAAe1P,SAASC,cAAc,OAC5CyP,EAAaxP,WAAa,sBAC1B,IAAMyP,EAAe,GAAHxZ,OAAM6Y,EAAKnY,QAAQkM,WAAU,MAAA5M,OAAK0Y,EAAQ,KAAA1Y,OAAImZ,GAChEI,EAAapG,aAAa,aAAcqG,GAKxC,IAAMC,EAAoBX,EAAY3E,OACpCkF,EACAC,EAAmBD,EAAqB,GAE1CJ,GAAmBK,EAAmBD,EAKtCI,EAAkBhS,QAAQ,SAACiS,GAAO,OAChCH,EAAa3O,YAAY8O,EAAQ,GAMnCZ,EAAY3E,OAAOkF,EAAoB,EAAGE,GAK1Cd,EAAOhO,UAAY,GAKnBqO,EAAYrR,QAAQ,SAACiS,GAAO,OAAKjB,EAAO7N,YAAY8O,EAAQ,GAExDb,EAAKnY,QAAQW,OACfC,QAAQC,IACN,kBACAkY,EACAJ,EACAC,EACAL,EAAkB,EAGxB,GAGKR,CACT,GAAC,CAAAxa,IAAA,SAAAa,MAgBD,WAAS,IAAA6a,EAAA,KAIP7b,KAAK+Y,YAKA/Y,KAAKgZ,aACRhZ,KAAKga,oBAMPha,KAAKma,eAEL,IAAM2B,EAAc,aAAH5Z,OAAgBlC,KAAK4C,QAAQkM,YACxCiN,EAAS/b,KAAK4C,QAAQmZ,QCjuDvB,CACLC,QAAS,CACP,mCACA,mCACA,uCACA,sCACA,kBAEFC,MAAO,CACL,mCACA,kCACA,uCACA,sCACA,mBDqtDIzP,EAAiBxM,KAAK4C,QAAQ4J,iBAAkB,EAChD0P,EAAsB1P,EAAiB,kBAAoB,GAC3D0N,EAAiBla,KAAK4C,QAAQsX,iBAAkB,EAChDiC,EAA6Bnc,KAAK4C,QAAQuZ,2BAKhDnc,KAAK4N,YAAY3B,UAAYjM,KAAKoc,uBAChCpc,KAAK4C,QAAQmM,MACb+M,EACA9b,KAAKmP,sBACL+M,GAMFlc,KAAK4N,YAAYyH,aAAa,kBAAmBrV,KAAKuP,qBAKtDvP,KAAKoV,gBAAkBrJ,SAASC,cAAc,OAC9ChM,KAAKoV,gBAAgBnJ,UAAY,UAKjC8P,EAAO/b,KAAK4C,QAAQkM,YAAc9O,KAAKqP,aAAa1F,QAClD,SAAC0S,EAAaC,GACZ,IAAIC,EAAWF,EAAI1W,MAAM,KAMvBkW,EAAKjZ,QAAQsM,mBACb2M,EAAKjZ,QAAQsM,kBACX2M,EAAKjZ,QAAQkM,YAAc+M,EAAKxM,eAGlCkN,EAAWA,EAAS/N,OAClB,SAAC7F,GAAU,OACTkT,EAAKjZ,QAAQsM,oBACZ2M,EAAKjZ,QAAQsM,kBACZ2M,EAAKjZ,QAAQkM,YAAc+M,EAAKxM,aAChCzN,SAAS+G,EAAW,IAO5B,IAAIgS,EAAS5O,SAASC,cAAc,OACpC2O,EAAO1O,WAAa,SAKpB,IAAM4O,EAAkC,GAClCC,EAAgC,GAKtCyB,EAAS5S,QAAQ,SAAClI,EAAQ+a,GAAW,IAAAC,EAI7BC,GACHP,GACiB,iBAAX1a,GACPA,EAAOC,OAAS,GACQ,IAAxBD,EAAOkF,QAAQ,KAEXgW,GACHR,GACiB,iBAAX1a,GACPA,EAAOC,OAAS,GAChBD,EAAOkF,QAAQ,OAASlF,EAAOC,OAAS,EAKtCgb,IACF7B,EAAsBhF,KAAK2G,GAK3B/a,EAASA,EAAOO,QAAQ,MAAO,KAG7B2a,IACF7B,EAAoBjF,KAAK2G,GAKzB/a,EAASA,EAAOO,QAAQ,MAAO,KAMjC,IAAM4a,EAAcf,EAAKlR,UAAUkS,eAAepb,GAC5Cqb,EAAoBjB,EAAKlR,UAAUoS,qBACvCtb,EACAoa,EAAKjZ,QAAQT,QACb0Z,EAAKjZ,QAAQoa,cAMTC,EAAapB,EAAKjZ,QAAQsa,aAAe,SAAW,MACpDxU,EAAYqD,SAASC,cAAciR,GACzCvU,EAAUuD,WAAa,aAAJ/J,OAAiB0a,IAKpCH,EAAA/T,EAAUuE,WAAUC,IAAGlK,MAAAyZ,EAAAxW,EAAI4V,EAAKsB,sBAAsB1b,KAKtDoa,EAAKuB,uBACH3b,EACA,SAAC6X,EAAmBtY,GAClB0H,EAAU2M,aAAaiE,EAAWtY,EACpC,GAGF6a,EAAKrI,kBAAoB,mBAOvBqI,EAAKlR,UAAUsP,0BACdzN,GACA0N,EAmBG1N,GAIF9D,EAAUkE,aAAe,SAAC5F,GACxB6U,EAAK3R,oBAAoBzI,EAAQuF,GACjC6U,EAAKwB,sBAAsB5b,EAAQuF,EACrC,EACA0B,EAAUgQ,WAAa,SAAC1R,GACtB6U,EAAKyB,oBAAoB7b,EAAQuF,EACnC,EACA0B,EAAUiQ,cAAgB,SAAC3R,GACzB6U,EAAKyB,oBAAoB7b,EAAQuF,EACnC,IAKA0B,EAAUmE,QAAU,SAAC7F,GACnB6U,EAAKjI,cAAa,GAOsB,mBAA/BiI,EAAKjZ,QAAQ0R,eAClBuH,EAAKjZ,QAAQsX,gBAAkB2B,EAAKjZ,QAAQ2a,kBAE9C1B,EAAK3R,oBAAoBzI,EAAQuF,EAErC,EACA0B,EAAUoQ,YAAc,SAAC9R,IAMmB,mBAA/B6U,EAAKjZ,QAAQ0R,eACnBuH,EAAKjZ,QAAQsX,gBAAkB2B,EAAKjZ,QAAQ2a,oBAE9C1B,EAAKzI,aAENyI,EAAK3R,oBAAoBzI,EAAQuF,GAEnC6U,EAAKwB,sBAAsB5b,EAAQuF,EACrC,EACA0B,EAAUkQ,UAAY,SAAC5R,GACrB6U,EAAKyB,oBAAoB7b,EAAQuF,EACnC,IA/DF0B,EAAUoB,cAAgB,SAAC9C,GACzB6U,EAAK3R,oBAAoBzI,EAAQuF,GACjC6U,EAAKwB,sBAAsB5b,EAAQuF,EACrC,EACA0B,EAAU6B,YAAc,SAACvD,GACvB6U,EAAKyB,oBAAoB7b,EAAQuF,EACnC,EACA0B,EAAUmQ,gBAAkB,SAAC7R,GAC3B6U,EAAKyB,oBAAoB7b,EAAQuF,EACnC,GA6DF0B,EAAU2M,aAAa,aAAc5T,GAMrC,IAAM+b,EAAY,GAAHtb,OAAM2Z,EAAKjZ,QAAQkM,WAAU,MAAA5M,OAAKoa,EAAM,KAAApa,OAAIsa,GAC3D9T,EAAU2M,aAAa,gBAAiBmI,GAKxC,IAAMC,EAAgB1R,SAASC,cAAc,QAC7CyR,EAAc9Q,UAAYmQ,EAC1BpU,EAAUoE,YAAY2Q,GAKjB5B,EAAKvM,eAAe7N,KAASoa,EAAKvM,eAAe7N,GAAU,IAEhEoa,EAAKvM,eAAe7N,GAAQoU,KAAKnN,GAKjCiS,EAAO7N,YAAYpE,EACrB,GAKAiS,EAASkB,EAAK6B,sBACZ/C,EACA2B,EACAzB,EACAC,GAMFe,EAAKzG,gBAAgBtI,YAAY6N,EACnC,GAMF3a,KAAK4N,YAAYd,YAAY9M,KAAKoV,iBAKlCpV,KAAKoa,WAEApa,KAAKgZ,cAIRhZ,KAAKgZ,aAAc,GAOjBhZ,KAAK2K,UAAUsP,0BACdzN,GACA0N,EAMQ1N,GAITT,SAAS2M,WAAa,SAAC1R,GAAuB,OAC5C6U,EAAKyB,yBAAoBjb,EAAW2E,EAAE,EACxC+E,SAAS4M,cAAgB,SAAC3R,GAAuB,OAC/C6U,EAAKyB,yBAAoBjb,EAAW2E,EAAE,EAExChH,KAAK4N,YAAYhB,aAAe,SAAC5F,GAAuB,OACtD6U,EAAK8B,iCAAiC3W,EAAE,GAChCwF,IAIVT,SAAS6M,UAAY,SAAC5R,GAAuB,OAC3C6U,EAAKyB,yBAAoBjb,EAAW2E,EAAE,EACxChH,KAAK4N,YAAYkL,YAAc,SAAC9R,GAAuB,OACrD6U,EAAK8B,iCAAiC3W,EAAE,IAtB1C+E,SAASxB,YAAc,SAACvD,GAAuB,OAC7C6U,EAAKyB,yBAAoBjb,EAAW2E,EAAE,EACxChH,KAAK4N,YAAY9D,cAAgB,SAAC9C,GAAuB,OACvD6U,EAAK8B,iCAAiC3W,EAAE,GAyB5ChH,KAAK6Z,SAET,I,4FAAC,CA/gEiB,GAkhEpB,IEtiEA,I", "sources": ["webpack://SimpleKeyboard/webpack/universalModuleDefinition", "webpack://SimpleKeyboard/webpack/bootstrap", "webpack://SimpleKeyboard/webpack/runtime/define property getters", "webpack://SimpleKeyboard/webpack/runtime/hasOwnProperty shorthand", "webpack://SimpleKeyboard/webpack/runtime/make namespace object", "webpack://SimpleKeyboard/./src/lib/services/Utilities.ts", "webpack://SimpleKeyboard/./src/lib/services/PhysicalKeyboard.ts", "webpack://SimpleKeyboard/./src/lib/components/CandidateBox.ts", "webpack://SimpleKeyboard/./src/lib/components/Keyboard.ts", "webpack://SimpleKeyboard/./src/lib/services/KeyboardLayout.ts", "webpack://SimpleKeyboard/./src/lib/index.modern.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"SimpleKeyboard\"] = factory();\n\telse\n\t\troot[\"SimpleKeyboard\"] = factory();\n})(this, function() {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { KeyboardInput } from \"./../interfaces\";\nimport { KeyboardOptions, UtilitiesParams } from \"../interfaces\";\n\n/**\n * Utility Service\n */\nclass Utilities {\n  getOptions: () => KeyboardOptions;\n  getCaretPosition: () => number | null;\n  getCaretPositionEnd: () => number | null;\n  dispatch: any;\n  maxLengthReached!: boolean;\n\n  /**\n   * Creates an instance of the Utility service\n   */\n  constructor({\n    getOptions,\n    getCaretPosition,\n    getCaretPositionEnd,\n    dispatch,\n  }: UtilitiesParams) {\n    this.getOptions = getOptions;\n    this.getCaretPosition = getCaretPosition;\n    this.getCaretPositionEnd = getCaretPositionEnd;\n    this.dispatch = dispatch;\n\n    /**\n     * Bindings\n     */\n    Utilities.bindMethods(Utilities, this);\n  }\n\n  /**\n   * Retrieve button type\n   *\n   * @param  {string} button The button's layout name\n   * @return {string} The button type\n   */\n  getButtonType(button: string): string {\n    return button.includes(\"{\") && button.includes(\"}\") && button !== \"{//}\"\n      ? \"functionBtn\"\n      : \"standardBtn\";\n  }\n\n  /**\n   * Adds default classes to a given button\n   *\n   * @param  {string} button The button's layout name\n   * @return {string} The classes to be added to the button\n   */\n  getButtonClass(button: string): string {\n    const buttonTypeClass = this.getButtonType(button);\n    const buttonWithoutBraces = button.replace(\"{\", \"\").replace(\"}\", \"\");\n    let buttonNormalized = \"\";\n\n    if (buttonTypeClass !== \"standardBtn\")\n      buttonNormalized = ` hg-button-${buttonWithoutBraces}`;\n\n    return `hg-${buttonTypeClass}${buttonNormalized}`;\n  }\n\n  /**\n   * Default button display labels\n   */\n  getDefaultDiplay() {\n    return {\n      \"{bksp}\": \"backspace\",\n      \"{backspace}\": \"backspace\",\n      \"{enter}\": \"< enter\",\n      \"{shift}\": \"shift\",\n      \"{shiftleft}\": \"shift\",\n      \"{shiftright}\": \"shift\",\n      \"{alt}\": \"alt\",\n      \"{s}\": \"shift\",\n      \"{tab}\": \"tab\",\n      \"{lock}\": \"caps\",\n      \"{capslock}\": \"caps\",\n      \"{accept}\": \"Submit\",\n      \"{space}\": \" \",\n      \"{//}\": \" \",\n      \"{esc}\": \"esc\",\n      \"{escape}\": \"esc\",\n      \"{f1}\": \"f1\",\n      \"{f2}\": \"f2\",\n      \"{f3}\": \"f3\",\n      \"{f4}\": \"f4\",\n      \"{f5}\": \"f5\",\n      \"{f6}\": \"f6\",\n      \"{f7}\": \"f7\",\n      \"{f8}\": \"f8\",\n      \"{f9}\": \"f9\",\n      \"{f10}\": \"f10\",\n      \"{f11}\": \"f11\",\n      \"{f12}\": \"f12\",\n      \"{numpaddivide}\": \"/\",\n      \"{numlock}\": \"lock\",\n      \"{arrowup}\": \"↑\",\n      \"{arrowleft}\": \"←\",\n      \"{arrowdown}\": \"↓\",\n      \"{arrowright}\": \"→\",\n      \"{prtscr}\": \"print\",\n      \"{scrolllock}\": \"scroll\",\n      \"{pause}\": \"pause\",\n      \"{insert}\": \"ins\",\n      \"{home}\": \"home\",\n      \"{pageup}\": \"up\",\n      \"{delete}\": \"del\",\n      \"{forwarddelete}\": \"del\",\n      \"{end}\": \"end\",\n      \"{pagedown}\": \"down\",\n      \"{numpadmultiply}\": \"*\",\n      \"{numpadsubtract}\": \"-\",\n      \"{numpadadd}\": \"+\",\n      \"{numpadenter}\": \"enter\",\n      \"{period}\": \".\",\n      \"{numpaddecimal}\": \".\",\n      \"{numpad0}\": \"0\",\n      \"{numpad1}\": \"1\",\n      \"{numpad2}\": \"2\",\n      \"{numpad3}\": \"3\",\n      \"{numpad4}\": \"4\",\n      \"{numpad5}\": \"5\",\n      \"{numpad6}\": \"6\",\n      \"{numpad7}\": \"7\",\n      \"{numpad8}\": \"8\",\n      \"{numpad9}\": \"9\",\n    };\n  }\n  /**\n   * Returns the display (label) name for a given button\n   *\n   * @param  {string} button The button's layout name\n   * @param  {object} display The provided display option\n   * @param  {boolean} mergeDisplay Whether the provided param value should be merged with the default one.\n   */\n  getButtonDisplayName(\n    button: string,\n    display: KeyboardOptions[\"display\"],\n    mergeDisplay = false\n  ) {\n    if (mergeDisplay) {\n      display = Object.assign({}, this.getDefaultDiplay(), display);\n    } else {\n      display = display || this.getDefaultDiplay();\n    }\n\n    return display[button] || button;\n  }\n\n  /**\n   * Returns the updated input resulting from clicking a given button\n   *\n   * @param  {string} button The button's layout name\n   * @param  {string} input The input string\n   * @param  {number} caretPos The cursor's current position\n   * @param  {number} caretPosEnd The cursor's current end position\n   * @param  {boolean} moveCaret Whether to update simple-keyboard's cursor\n   */\n  getUpdatedInput(\n    button: string,\n    input: string,\n    caretPos: any,\n    caretPosEnd = caretPos,\n    moveCaret = false\n  ) {\n    const options = this.getOptions();\n    const commonParams: [number | undefined, number | undefined, boolean] = [\n      caretPos,\n      caretPosEnd,\n      moveCaret,\n    ];\n\n    let output = input;\n\n    if (\n      (button === \"{bksp}\" || button === \"{backspace}\") &&\n      output.length > 0\n    ) {\n      output = this.removeAt(output, ...commonParams);\n    } else if (\n      (button === \"{delete}\" || button === \"{forwarddelete}\") &&\n      output.length > 0\n    ) {\n      output = this.removeForwardsAt(output, ...commonParams);\n    } else if (button === \"{space}\")\n      output = this.addStringAt(output, \" \", ...commonParams);\n    else if (\n      button === \"{tab}\" &&\n      !(\n        typeof options.tabCharOnTab === \"boolean\" &&\n        options.tabCharOnTab === false\n      )\n    ) {\n      output = this.addStringAt(output, \"\\t\", ...commonParams);\n    } else if (\n      (button === \"{enter}\" || button === \"{numpadenter}\") &&\n      options.newLineOnEnter\n    )\n      output = this.addStringAt(output, \"\\n\", ...commonParams);\n    else if (\n      button.includes(\"numpad\") &&\n      Number.isInteger(Number(button[button.length - 2]))\n    ) {\n      output = this.addStringAt(\n        output,\n        button[button.length - 2],\n        ...commonParams\n      );\n    } else if (button === \"{numpaddivide}\")\n      output = this.addStringAt(output, \"/\", ...commonParams);\n    else if (button === \"{numpadmultiply}\")\n      output = this.addStringAt(output, \"*\", ...commonParams);\n    else if (button === \"{numpadsubtract}\")\n      output = this.addStringAt(output, \"-\", ...commonParams);\n    else if (button === \"{numpadadd}\")\n      output = this.addStringAt(output, \"+\", ...commonParams);\n    else if (button === \"{numpaddecimal}\")\n      output = this.addStringAt(output, \".\", ...commonParams);\n    else if (button === \"{\" || button === \"}\")\n      output = this.addStringAt(output, button, ...commonParams);\n    else if (!button.includes(\"{\") && !button.includes(\"}\"))\n      output = this.addStringAt(output, button, ...commonParams);\n\n    if(options.debug){\n      console.log(\"Input will be: \"+ output);\n    }\n\n    return output;\n  }\n\n  /**\n   * Moves the cursor position by a given amount\n   *\n   * @param  {number} length Represents by how many characters the input should be moved\n   * @param  {boolean} minus Whether the cursor should be moved to the left or not.\n   */\n  updateCaretPos(length: number, minus = false) {\n    const newCaretPos = this.updateCaretPosAction(length, minus);\n\n    this.dispatch((instance: any) => {\n      instance.setCaretPosition(newCaretPos);\n    });\n  }\n\n  /**\n   * Action method of updateCaretPos\n   *\n   * @param  {number} length Represents by how many characters the input should be moved\n   * @param  {boolean} minus Whether the cursor should be moved to the left or not.\n   */\n  updateCaretPosAction(length: number, minus = false) {\n    const options = this.getOptions();\n    let caretPosition = this.getCaretPosition();\n\n    if (caretPosition != null) {\n      if (minus) {\n        if (caretPosition > 0) caretPosition = caretPosition - length;\n      } else {\n        caretPosition = caretPosition + length;\n      }\n    }\n\n    if (options.debug) {\n      console.log(\"Caret at:\", caretPosition);\n    }\n\n    return caretPosition;\n  }\n\n  /**\n   * Adds a string to the input at a given position\n   *\n   * @param  {string} source The source input\n   * @param  {string} str The string to add\n   * @param  {number} position The (cursor) position where the string should be added\n   * @param  {boolean} moveCaret Whether to update simple-keyboard's cursor\n   */\n  addStringAt(\n    source: string,\n    str: string,\n    position = source.length,\n    positionEnd = source.length,\n    moveCaret = false\n  ) {\n    let output;\n\n    if (!position && position !== 0) {\n      output = source + str;\n    } else {\n      output = [source.slice(0, position), str, source.slice(positionEnd)].join(\n        \"\"\n      );\n\n      /**\n       * Avoid caret position change when maxLength is set\n       */\n      if (!this.isMaxLengthReached()) {\n        if (moveCaret) this.updateCaretPos(str.length);\n      }\n    }\n\n    return output;\n  }\n\n  /**\n   * Check whether the button is a standard button\n   */\n  isStandardButton = (button: string) =>\n    button && !(button[0] === \"{\" && button[button.length - 1] === \"}\");\n\n  /**\n   * Removes an amount of characters before a given position\n   *\n   * @param  {string} source The source input\n   * @param  {number} position The (cursor) position from where the characters should be removed\n   * @param  {boolean} moveCaret Whether to update simple-keyboard's cursor\n   */\n  removeAt(\n    source: string,\n    position = source.length,\n    positionEnd = source.length,\n    moveCaret = false\n  ) {\n    if (position === 0 && positionEnd === 0) {\n      return source;\n    }\n\n    let output;\n\n    if (position === positionEnd) {\n      let prevTwoChars;\n      let emojiMatched;\n      const emojiMatchedReg = /([\\uD800-\\uDBFF][\\uDC00-\\uDFFF])/g;\n\n      /**\n       * Emojis are made out of two characters, so we must take a custom approach to trim them.\n       * For more info: https://mathiasbynens.be/notes/javascript-unicode\n       */\n      if (position && position >= 0) {\n        prevTwoChars = source.substring(position - 2, position);\n        emojiMatched = prevTwoChars.match(emojiMatchedReg);\n\n        if (emojiMatched) {\n          output = source.substr(0, position - 2) + source.substr(position);\n          if (moveCaret) this.updateCaretPos(2, true);\n        } else {\n          output = source.substr(0, position - 1) + source.substr(position);\n          if (moveCaret) this.updateCaretPos(1, true);\n        }\n      } else {\n        prevTwoChars = source.slice(-2);\n        emojiMatched = prevTwoChars.match(emojiMatchedReg);\n\n        if (emojiMatched) {\n          output = source.slice(0, -2);\n          if (moveCaret) this.updateCaretPos(2, true);\n        } else {\n          output = source.slice(0, -1);\n          if (moveCaret) this.updateCaretPos(1, true);\n        }\n      }\n    } else {\n      output = source.slice(0, position) + source.slice(positionEnd);\n      if (moveCaret) {\n        this.dispatch((instance: any) => {\n          instance.setCaretPosition(position);\n        });\n      }\n    }\n\n    return output;\n  }\n\n  /**\n   * Removes an amount of characters after a given position\n   *\n   * @param  {string} source The source input\n   * @param  {number} position The (cursor) position from where the characters should be removed\n   */\n  removeForwardsAt(\n    source: string,\n    position: number = source.length,\n    positionEnd: number = source.length,\n    moveCaret = false\n  ) {\n    if (!source?.length || position === null) {\n      return source;\n    }\n\n    let output;\n\n    if (position === positionEnd) {\n      const emojiMatchedReg = /([\\uD800-\\uDBFF][\\uDC00-\\uDFFF])/g;\n\n      /**\n       * Emojis are made out of two characters, so we must take a custom approach to trim them.\n       * For more info: https://mathiasbynens.be/notes/javascript-unicode\n       */\n      const nextTwoChars = source.substring(position, position + 2);\n      const emojiMatched = nextTwoChars.match(emojiMatchedReg);\n\n      if (emojiMatched) {\n        output = source.substr(0, position) + source.substr(position + 2);\n      } else {\n        output = source.substr(0, position) + source.substr(position + 1);\n      }\n    } else {\n      output = source.slice(0, position) + source.slice(positionEnd);\n      if (moveCaret) {\n        this.dispatch((instance: any) => {\n          instance.setCaretPosition(position);\n        });\n      }\n    }\n\n    return output;\n  }\n\n  /**\n   * Determines whether the maxLength has been reached. This function is called when the maxLength option it set.\n   *\n   * @param  {object} inputObj\n   * @param  {string} updatedInput\n   */\n  handleMaxLength(inputObj: KeyboardInput, updatedInput: string) {\n    const options = this.getOptions();\n    const maxLength = options.maxLength;\n    const currentInput = inputObj[options.inputName || \"default\"];\n    const condition = updatedInput.length - 1 >= maxLength;\n\n    if (\n      /**\n       * If pressing this button won't add more characters\n       * We exit out of this limiter function\n       */\n      updatedInput.length <= currentInput.length\n    ) {\n      return false;\n    }\n\n    if (Number.isInteger(maxLength)) {\n      if (options.debug) {\n        console.log(\"maxLength (num) reached:\", condition);\n      }\n\n      if (condition) {\n        /**\n         * @type {boolean} Boolean value that shows whether maxLength has been reached\n         */\n        this.maxLengthReached = true;\n        return true;\n      } else {\n        this.maxLengthReached = false;\n        return false;\n      }\n    }\n\n    if (typeof maxLength === \"object\") {\n      const condition =\n        updatedInput.length - 1 >= maxLength[options.inputName || \"default\"];\n\n      if (options.debug) {\n        console.log(\"maxLength (obj) reached:\", condition);\n      }\n\n      if (condition) {\n        this.maxLengthReached = true;\n        return true;\n      } else {\n        this.maxLengthReached = false;\n        return false;\n      }\n    }\n  }\n\n  /**\n   * Gets the current value of maxLengthReached\n   */\n  isMaxLengthReached() {\n    return Boolean(this.maxLengthReached);\n  }\n\n  /**\n   * Determines whether a touch device is being used\n   */\n  isTouchDevice() {\n    return \"ontouchstart\" in window || navigator.maxTouchPoints;\n  }\n\n  /**\n   * Determines whether pointer events are supported\n   */\n  pointerEventsSupported() {\n    return !!window.PointerEvent;\n  }\n\n  /**\n   * Bind all methods in a given class\n   */\n\n  static bindMethods(myClass: any, instance: any) {\n    // eslint-disable-next-line no-unused-vars\n    for (const myMethod of Object.getOwnPropertyNames(myClass.prototype)) {\n      const excludeMethod =\n        myMethod === \"constructor\" || myMethod === \"bindMethods\";\n      if (!excludeMethod) {\n        instance[myMethod] = instance[myMethod].bind(instance);\n      }\n    }\n  }\n\n  /**\n   * Transforms an arbitrary string to camelCase\n   *\n   * @param  {string} str The string to transform.\n   */\n  camelCase(str: string): string {\n    if (!str) return \"\";\n\n    return str\n      .toLowerCase()\n      .trim()\n      .split(/[.\\-_\\s]/g)\n      .reduce((str, word) =>\n        word.length ? str + word[0].toUpperCase() + word.slice(1) : str\n      );\n  }\n\n  /**\n   * Split array into chunks\n   */\n  chunkArray<T>(arr: T[], size: number): T[][] {\n    return [...Array(Math.ceil(arr.length / size))].map((_, i) =>\n      arr.slice(size * i, size + size * i)\n    );\n  }\n\n  /**\n   * Escape regex input\n   */\n  escapeRegex(str: string) {\n    return str.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\");\n  }\n\n  /**\n   * Calculate caret position offset when using rtl option\n   */\n  getRtlOffset(index: number, input: string) {\n    let newIndex = index;\n    const startMarkerIndex = input.indexOf(\"\\u202B\");\n    const endMarkerIndex = input.indexOf(\"\\u202C\");\n\n    if(startMarkerIndex < index && startMarkerIndex != -1){ newIndex--; }\n    if(endMarkerIndex < index && startMarkerIndex != -1){ newIndex--; }\n\n    return newIndex < 0 ? 0 : newIndex;\n  }\n\n  /**\n   * Reusable empty function\n   */\n  static noop = () => {};\n\n  /**\n   * Check if a function is a constructor\n   */\n  isConstructor(f: any) {\n    try {\n      Reflect.construct(String, [], f);\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }\n}\n\nexport default Utilities;\n", "import { KeyboardOptions, PhysicalKeyboardParams } from \"../interfaces\";\nimport Utilities from \"../services/Utilities\";\n\n/**\n * Physical Keyboard Service\n */\nclass PhysicalKeyboard {\n  getOptions: () => KeyboardOptions;\n  dispatch: any;\n\n  /**\n   * Creates an instance of the PhysicalKeyboard service\n   */\n  constructor({ dispatch, getOptions }: PhysicalKeyboardParams) {\n    /**\n     * @type {object} A simple-keyboard instance\n     */\n    this.dispatch = dispatch;\n    this.getOptions = getOptions;\n\n    /**\n     * Bindings\n     */\n    Utilities.bindMethods(PhysicalKeyboard, this);\n  }\n\n  handleHighlightKeyDown(e: KeyboardEvent) {\n    const options = this.getOptions();\n\n    if(options.physicalKeyboardHighlightPreventDefault && this.isModifierKey(e)){\n      e.preventDefault();\n      e.stopImmediatePropagation();\n    }\n\n    const buttonPressed = this.getSimpleKeyboardLayoutKey(e);\n\n    this.dispatch((instance: any) => {\n      const standardButtonPressed = instance.getButtonElement(buttonPressed);\n      const functionButtonPressed = instance.getButtonElement(\n        `{${buttonPressed}}`\n      );\n      let buttonDOM;\n      let buttonName: string;\n\n      if (standardButtonPressed) {\n        buttonDOM = standardButtonPressed;\n        buttonName = buttonPressed;\n      } else if (functionButtonPressed) {\n        buttonDOM = functionButtonPressed;\n        buttonName = `{${buttonPressed}}`;\n      } else {\n        return;\n      }\n\n      const applyButtonStyle = (buttonElement: HTMLElement) => {\n        buttonElement.style.background =\n          options.physicalKeyboardHighlightBgColor || \"#dadce4\";\n          buttonElement.style.color =\n          options.physicalKeyboardHighlightTextColor || \"black\";\n      }\n\n      if (buttonDOM) {\n        if(Array.isArray(buttonDOM)){\n          buttonDOM.forEach(buttonElement => applyButtonStyle(buttonElement));\n\n          // Even though we have an array of buttons, we just want to press one of them\n          if (options.physicalKeyboardHighlightPress) {\n            if (options.physicalKeyboardHighlightPressUsePointerEvents) {\n              buttonDOM[0]?.onpointerdown?.(e);\n            } else if (options.physicalKeyboardHighlightPressUseClick) {\n              buttonDOM[0]?.click();\n            } else {\n              instance.handleButtonClicked(buttonName, e);\n            }\n          }\n        } else {\n          applyButtonStyle(buttonDOM);\n\n          if (options.physicalKeyboardHighlightPress) {\n            if (options.physicalKeyboardHighlightPressUsePointerEvents) {\n              buttonDOM?.onpointerdown?.(e);\n            } else if (options.physicalKeyboardHighlightPressUseClick) {\n              buttonDOM.click();\n            } else {\n              instance.handleButtonClicked(buttonName, e);\n            }\n          }\n        }\n      }\n    });\n  }\n\n  handleHighlightKeyUp(e: KeyboardEvent) {\n    const options = this.getOptions();\n\n    if(options.physicalKeyboardHighlightPreventDefault && this.isModifierKey(e)){\n      e.preventDefault();\n      e.stopImmediatePropagation();\n    }\n    \n    const buttonPressed = this.getSimpleKeyboardLayoutKey(e);\n\n    this.dispatch((instance: any) => {\n      const buttonDOM =\n        instance.getButtonElement(buttonPressed) ||\n        instance.getButtonElement(`{${buttonPressed}}`);\n\n      const applyButtonStyle = (buttonElement: HTMLElement) => {\n        if(buttonElement.removeAttribute){\n          buttonElement.removeAttribute(\"style\");\n        }\n      };\n\n      if (buttonDOM) {\n        if(Array.isArray(buttonDOM)){\n          buttonDOM.forEach(buttonElement => applyButtonStyle(buttonElement));\n\n          // Even though we have an array of buttons, we just want to press one of them\n          if (options.physicalKeyboardHighlightPressUsePointerEvents) {\n            buttonDOM[0]?.onpointerup?.(e);\n          }\n        } else {\n          applyButtonStyle(buttonDOM);\n\n          if (options.physicalKeyboardHighlightPressUsePointerEvents) {\n            buttonDOM?.onpointerup?.(e);\n          }\n        }\n      }\n    });\n  }\n\n  /**\n   * Transforms a KeyboardEvent's \"key.code\" string into a simple-keyboard layout format\n   * @param  {object} e The KeyboardEvent\n   */\n  getSimpleKeyboardLayoutKey(e: KeyboardEvent) {\n    let output = \"\";\n    const keyId = e.code || e.key || this.keyCodeToKey(e?.keyCode);\n\n    if (\n      keyId?.includes(\"Numpad\") ||\n      keyId?.includes(\"Shift\") ||\n      keyId?.includes(\"Space\") ||\n      keyId?.includes(\"Backspace\") ||\n      keyId?.includes(\"Control\") ||\n      keyId?.includes(\"Alt\") ||\n      keyId?.includes(\"Meta\")\n    ) {\n      output = e.code || \"\";\n    } else {\n      output = e.key || this.keyCodeToKey(e?.keyCode) || \"\";\n    }\n\n    return output.length > 1 ? output?.toLowerCase() : output;\n  }\n\n  /**\n   * Retrieve key from keyCode\n   */\n  keyCodeToKey(keyCode: number): string {\n    return {\n      8: \"Backspace\",\n      9: \"Tab\",\n      13: \"Enter\",\n      16: \"Shift\",\n      17: \"Ctrl\",\n      18: \"Alt\",\n      19: \"Pause\",\n      20: \"CapsLock\",\n      27: \"Esc\",\n      32: \"Space\",\n      33: \"PageUp\",\n      34: \"PageDown\",\n      35: \"End\",\n      36: \"Home\",\n      37: \"ArrowLeft\",\n      38: \"ArrowUp\",\n      39: \"ArrowRight\",\n      40: \"ArrowDown\",\n      45: \"Insert\",\n      46: \"Delete\",\n      48: \"0\",\n      49: \"1\",\n      50: \"2\",\n      51: \"3\",\n      52: \"4\",\n      53: \"5\",\n      54: \"6\",\n      55: \"7\",\n      56: \"8\",\n      57: \"9\",\n      65: \"A\",\n      66: \"B\",\n      67: \"C\",\n      68: \"D\",\n      69: \"E\",\n      70: \"F\",\n      71: \"G\",\n      72: \"H\",\n      73: \"I\",\n      74: \"J\",\n      75: \"K\",\n      76: \"L\",\n      77: \"M\",\n      78: \"N\",\n      79: \"O\",\n      80: \"P\",\n      81: \"Q\",\n      82: \"R\",\n      83: \"S\",\n      84: \"T\",\n      85: \"U\",\n      86: \"V\",\n      87: \"W\",\n      88: \"X\",\n      89: \"Y\",\n      90: \"Z\",\n      91: \"Meta\",\n      96: \"Numpad0\",\n      97: \"Numpad1\",\n      98: \"Numpad2\",\n      99: \"Numpad3\",\n      100: \"Numpad4\",\n      101: \"Numpad5\",\n      102: \"Numpad6\",\n      103: \"Numpad7\",\n      104: \"Numpad8\",\n      105: \"Numpad9\",\n      106: \"NumpadMultiply\",\n      107: \"NumpadAdd\",\n      109: \"NumpadSubtract\",\n      110: \"NumpadDecimal\",\n      111: \"NumpadDivide\",\n      112: \"F1\",\n      113: \"F2\",\n      114: \"F3\",\n      115: \"F4\",\n      116: \"F5\",\n      117: \"F6\",\n      118: \"F7\",\n      119: \"F8\",\n      120: \"F9\",\n      121: \"F10\",\n      122: \"F11\",\n      123: \"F12\",\n      144: \"NumLock\",\n      145: \"ScrollLock\",\n      186: \";\",\n      187: \"=\",\n      188: \",\",\n      189: \"-\",\n      190: \".\",\n      191: \"/\",\n      192: \"`\",\n      219: \"[\",\n      220: \"\\\\\",\n      221: \"]\",\n      222: \"'\",\n    }[keyCode] || \"\";\n  }\n\n  isModifierKey = (e: KeyboardEvent): boolean => {\n    return (\n      e.altKey\n      || e.ctrlKey\n      || e.shiftKey\n      || [\"Tab\", \"CapsLock\", \"Esc\", \"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(\n        e.code || e.key || this.keyCodeToKey(e?.keyCode)\n      )\n    )\n  }\n}\n\nexport default PhysicalKeyboard;\n", "import \"./css/CandidateBox.css\";\n\nimport Utilities from \"../services/Utilities\";\nimport {\n  CandidateBoxParams,\n  CandidateBoxRenderParams,\n  CandidateBoxShowParams,\n  KeyboardOptions,\n} from \"./../interfaces\";\n\nclass CandidateBox {\n  utilities: Utilities;\n  options: KeyboardOptions;\n  candidateBoxElement!: HTMLDivElement;\n  pageIndex = 0;\n  pageSize: number;\n\n  constructor({ utilities, options }: CandidateBoxParams) {\n    this.utilities = utilities;\n    this.options = options;\n    Utilities.bindMethods(CandidateBox, this);\n    this.pageSize = this.utilities.getOptions().layoutCandidatesPageSize || 5;\n  }\n\n  destroy() {\n    if (this.candidateBoxElement) {\n      this.candidateBoxElement.remove();\n      this.pageIndex = 0;\n    }\n  }\n\n  show({\n    candidateValue,\n    targetElement,\n    onSelect,\n  }: CandidateBoxShowParams): void {\n    if (!candidateValue || !candidateValue.length) {\n      return;\n    }\n\n    const candidateListPages = this.utilities.chunkArray(\n      candidateValue.split(\" \"),\n      this.pageSize\n    );\n\n    this.renderPage({\n      candidateListPages,\n      targetElement,\n      pageIndex: this.pageIndex,\n      nbPages: candidateListPages.length,\n      onItemSelected: (selectedCandidate: string, e: MouseEvent) => {\n        onSelect(selectedCandidate, e);\n        this.destroy();\n      },\n    });\n  }\n\n  renderPage({\n    candidateListPages,\n    targetElement,\n    pageIndex,\n    nbPages,\n    onItemSelected,\n  }: CandidateBoxRenderParams) {\n    // Remove current candidate box, if any\n    this.candidateBoxElement?.remove();\n\n    // Create candidate box element\n    this.candidateBoxElement = document.createElement(\"div\");\n    this.candidateBoxElement.className = \"hg-candidate-box\";\n\n    // Candidate box list\n    const candidateListULElement = document.createElement(\"ul\");\n    candidateListULElement.className = \"hg-candidate-box-list\";\n\n    // Create Candidate box list items\n    candidateListPages[pageIndex].forEach((candidateListItem) => {\n      const candidateListLIElement = document.createElement(\"li\");\n      const getMouseEvent = () => {\n        const mouseEvent = new (this.options.useTouchEvents ? TouchEvent : MouseEvent)(\"click\");\n        Object.defineProperty(mouseEvent, \"target\", {\n          value: candidateListLIElement,\n        });\n        return mouseEvent;\n      };\n\n      candidateListLIElement.className = \"hg-candidate-box-list-item\";\n      candidateListLIElement.innerHTML = this.options.display?.[candidateListItem] || candidateListItem;\n\n      if(this.options.useTouchEvents) {\n        candidateListLIElement.ontouchstart = (e: any) =>\n          onItemSelected(candidateListItem, e || getMouseEvent());\n      } else {\n        candidateListLIElement.onclick = (e = getMouseEvent() as MouseEvent) =>\n          onItemSelected(candidateListItem, e);\n      }\n\n      // Append list item to ul\n      candidateListULElement.appendChild(candidateListLIElement);\n    });\n\n    // Add previous button\n    const isPrevBtnElementActive = pageIndex > 0;\n    const prevBtnElement = document.createElement(\"div\");\n    prevBtnElement.classList.add(\"hg-candidate-box-prev\");\n    isPrevBtnElementActive &&\n      prevBtnElement.classList.add(\"hg-candidate-box-btn-active\");\n\n    const prevBtnElementClickAction = () => {\n      if (!isPrevBtnElementActive) return;\n      this.renderPage({\n        candidateListPages,\n        targetElement,\n        pageIndex: pageIndex - 1,\n        nbPages,\n        onItemSelected,\n      });\n    };\n\n    if(this.options.useTouchEvents) {\n      prevBtnElement.ontouchstart = prevBtnElementClickAction;\n    } else {\n      prevBtnElement.onclick = prevBtnElementClickAction;\n    }\n    \n    this.candidateBoxElement.appendChild(prevBtnElement);\n\n    // Add elements to container\n    this.candidateBoxElement.appendChild(candidateListULElement);\n\n    // Add next button\n    const isNextBtnElementActive = pageIndex < nbPages - 1;\n    const nextBtnElement = document.createElement(\"div\");\n    nextBtnElement.classList.add(\"hg-candidate-box-next\");\n    isNextBtnElementActive &&\n      nextBtnElement.classList.add(\"hg-candidate-box-btn-active\");\n\n    const nextBtnElementClickAction = () => {\n      if (!isNextBtnElementActive) return;\n      this.renderPage({\n        candidateListPages,\n        targetElement,\n        pageIndex: pageIndex + 1,\n        nbPages,\n        onItemSelected,\n      });\n    };\n\n    if(this.options.useTouchEvents) {\n      nextBtnElement.ontouchstart = nextBtnElementClickAction;\n    } else {\n      nextBtnElement.onclick = nextBtnElementClickAction;\n    }\n\n    this.candidateBoxElement.appendChild(nextBtnElement);\n\n    // Append candidate box to target element\n    targetElement.prepend(this.candidateBoxElement);\n  }\n}\n\nexport default CandidateBox;\n", "import \"./css/Keyboard.css\";\n\nimport { getDefaultLayout } from \"../services/KeyboardLayout\";\nimport PhysicalKeyboard from \"../services/PhysicalKeyboard\";\nimport Utilities from \"../services/Utilities\";\nimport {\n  KeyboardOptions,\n  KeyboardInput,\n  KeyboardButtonElements,\n  KeyboardHandlerEvent,\n  KeyboardElement,\n  SKWindow,\n} from \"../interfaces\";\nimport CandidateBox from \"./CandidateBox\";\n\n/**\n * Root class for simple-keyboard.\n * This class:\n * - Parses the options\n * - Renders the rows and buttons\n * - Handles button functionality\n */\nclass SimpleKeyboard {\n  input!: KeyboardInput;\n  options!: KeyboardOptions;\n  utilities!: Utilities;\n  caretPosition!: number | null;\n  caretPositionEnd!: number | null;\n  keyboardDOM!: KeyboardElement;\n  keyboardPluginClasses!: string;\n  keyboardDOMClass!: string;\n  buttonElements!: KeyboardButtonElements;\n  currentInstanceName!: string;\n  allKeyboardInstances!: { [key: string]: SimpleKeyboard };\n  keyboardInstanceNames!: string[];\n  isFirstKeyboardInstance!: boolean;\n  physicalKeyboard!: PhysicalKeyboard;\n  modules!: { [key: string]: any };\n  activeButtonClass!: string;\n  holdInteractionTimeout!: number;\n  holdTimeout!: number;\n  isMouseHold!: boolean;\n  initialized!: boolean;\n  candidateBox!: CandidateBox | null;\n  keyboardRowsDOM!: KeyboardElement;\n  defaultName = \"default\";\n  activeInputElement: HTMLInputElement | HTMLTextAreaElement | null = null;\n\n  /**\n   * Creates an instance of SimpleKeyboard\n   * @param {Array} selectorOrOptions If first parameter is a string, it is considered the container class. The second parameter is then considered the options object. If first parameter is an object, it is considered the options object.\n   */\n  constructor(\n    selectorOrOptions?: string | HTMLDivElement | KeyboardOptions,\n    keyboardOptions?: KeyboardOptions\n  ) {\n    if (typeof window === \"undefined\") return;\n\n    const {\n      keyboardDOMClass,\n      keyboardDOM,\n      options = {},\n    } = this.handleParams(selectorOrOptions, keyboardOptions);\n\n    /**\n     * Initializing Utilities\n     */\n    this.utilities = new Utilities({\n      getOptions: this.getOptions,\n      getCaretPosition: this.getCaretPosition,\n      getCaretPositionEnd: this.getCaretPositionEnd,\n      dispatch: this.dispatch,\n    });\n\n    /**\n     * Caret position\n     */\n    this.caretPosition = null;\n\n    /**\n     * Caret position end\n     */\n    this.caretPositionEnd = null;\n\n    /**\n     * Processing options\n     */\n    this.keyboardDOM = keyboardDOM;\n\n    /**\n     * @type {object}\n     * @property {object} layout Modify the keyboard layout.\n     * @property {string} layoutName Specifies which layout should be used.\n     * @property {object} display Replaces variable buttons (such as {bksp}) with a human-friendly name (e.g.: “backspace”).\n     * @property {boolean} mergeDisplay By default, when you set the display property, you replace the default one. This setting merges them instead.\n     * @property {string} theme A prop to add your own css classes to the keyboard wrapper. You can add multiple classes separated by a space.\n     * @property {array} buttonTheme A prop to add your own css classes to one or several buttons.\n     * @property {array} buttonAttributes A prop to add your own attributes to one or several buttons.\n     * @property {boolean} debug Runs a console.log every time a key is pressed. Displays the buttons pressed and the current input.\n     * @property {boolean} newLineOnEnter Specifies whether clicking the “ENTER” button will input a newline (\\n) or not.\n     * @property {boolean} tabCharOnTab Specifies whether clicking the “TAB” button will input a tab character (\\t) or not.\n     * @property {string} inputName Allows you to use a single simple-keyboard instance for several inputs.\n     * @property {number} maxLength Restrains all of simple-keyboard inputs to a certain length. This should be used in addition to the input element’s maxlengthattribute.\n     * @property {object} maxLength Restrains simple-keyboard’s individual inputs to a certain length. This should be used in addition to the input element’s maxlengthattribute.\n     * @property {boolean} syncInstanceInputs When set to true, this option synchronizes the internal input of every simple-keyboard instance.\n     * @property {boolean} physicalKeyboardHighlight Enable highlighting of keys pressed on physical keyboard.\n     * @property {boolean} physicalKeyboardHighlightPress Presses keys highlighted by physicalKeyboardHighlight\n     * @property {string} physicalKeyboardHighlightTextColor Define the text color that the physical keyboard highlighted key should have.\n     * @property {string} physicalKeyboardHighlightBgColor Define the background color that the physical keyboard highlighted key should have.\n     * @property {boolean} physicalKeyboardHighlightPressUseClick Whether physicalKeyboardHighlightPress should use clicks to trigger buttons.\n     * @property {boolean} physicalKeyboardHighlightPressUsePointerEvents Whether physicalKeyboardHighlightPress should use pointer events to trigger buttons.\n     * @property {boolean} physicalKeyboardHighlightPreventDefault Whether physicalKeyboardHighlight should use preventDefault to disable default browser actions.\n     * @property {boolean} preventMouseDownDefault Calling preventDefault for the mousedown events keeps the focus on the input.\n     * @property {boolean} preventMouseUpDefault Calling preventDefault for the mouseup events.\n     * @property {boolean} stopMouseDownPropagation Stops pointer down events on simple-keyboard buttons from bubbling to parent elements.\n     * @property {boolean} stopMouseUpPropagation Stops pointer up events on simple-keyboard buttons from bubbling to parent elements.\n     * @property {function(button: string):string} onKeyPress Executes the callback function on key press. Returns button layout name (i.e.: “{shift}”).\n     * @property {function(input: string):string} onChange Executes the callback function on input change. Returns the current input’s string.\n     * @property {function} onRender Executes the callback function every time simple-keyboard is rendered (e.g: when you change layouts).\n     * @property {function} onInit Executes the callback function once simple-keyboard is rendered for the first time (on initialization).\n     * @property {function(keyboard: Keyboard):void} beforeInputUpdate Perform an action before any input change\n     * @property {function(inputs: object):object} onChangeAll Executes the callback function on input change. Returns the input object with all defined inputs.\n     * @property {boolean} useButtonTag Render buttons as a button element instead of a div element.\n     * @property {boolean} disableCaretPositioning A prop to ensure characters are always be added/removed at the end of the string.\n     * @property {object} inputPattern Restrains input(s) change to the defined regular expression pattern.\n     * @property {boolean} useTouchEvents Instructs simple-keyboard to use touch events instead of click events.\n     * @property {boolean} autoUseTouchEvents Enable useTouchEvents automatically when touch device is detected.\n     * @property {boolean} useMouseEvents Opt out of PointerEvents handling, falling back to the prior mouse event logic.\n     * @property {function} destroy Clears keyboard listeners and DOM elements.\n     * @property {boolean} disableButtonHold Disable button hold action.\n     * @property {boolean} rtl Adds unicode right-to-left control characters to input return values.\n     * @property {function} onKeyReleased Executes the callback function on key release.\n     * @property {array} modules Module classes to be loaded by simple-keyboard.\n     * @property {boolean} enableLayoutCandidates Enable input method editor candidate list support.\n     * @property {object} excludeFromLayout Buttons to exclude from layout\n     * @property {number} layoutCandidatesPageSize Determines size of layout candidate list\n     * @property {boolean} layoutCandidatesCaseSensitiveMatch Determines whether layout candidate match should be case sensitive.\n     * @property {boolean} disableCandidateNormalization Disables the automatic normalization for selected layout candidates\n     * @property {boolean} enableLayoutCandidatesKeyPress Enables onKeyPress triggering for layoutCandidate items\n     * @property {boolean} updateCaretOnSelectionChange Updates caret when selectionchange event is fired\n     * @property {boolean} clickOnMouseDown When useMouseEvents is enabled, this option allows you to trigger a button click event on mousedown\n     */\n    this.options = {\n      layoutName: \"default\",\n      theme: \"hg-theme-default\",\n      inputName: \"default\",\n      preventMouseDownDefault: false,\n      enableLayoutCandidates: true,\n      excludeFromLayout: {},\n      ...options,\n    };\n\n    /**\n     * @type {object} Classes identifying loaded plugins\n     */\n    this.keyboardPluginClasses = \"\";\n\n    /**\n     * Bindings\n     */\n    Utilities.bindMethods(SimpleKeyboard, this);\n\n    /**\n     * simple-keyboard uses a non-persistent internal input to keep track of the entered string (the variable `keyboard.input`).\n     * This removes any dependency to input DOM elements. You can type and directly display the value in a div element, for example.\n     * @example\n     * // To get entered input\n     * const input = keyboard.getInput();\n     *\n     * // To clear entered input.\n     * keyboard.clearInput();\n     *\n     * @type {object}\n     * @property {object} default Default SimpleKeyboard internal input.\n     * @property {object} myInputName Example input that can be set through `options.inputName:\"myInputName\"`.\n     */\n    const { inputName = this.defaultName } = this.options;\n    this.input = {};\n    this.input[inputName] = \"\";\n\n    /**\n     * @type {string} DOM class of the keyboard wrapper, normally \"simple-keyboard\" by default.\n     */\n    this.keyboardDOMClass = keyboardDOMClass;\n\n    /**\n     * @type {object} Contains the DOM elements of every rendered button, the key being the button's layout name (e.g.: \"{enter}\").\n     */\n    this.buttonElements = {};\n\n    /**\n     * Simple-keyboard Instances\n     * This enables multiple simple-keyboard support with easier management\n     */\n    if (!(window as SKWindow)[\"SimpleKeyboardInstances\"])\n      (window as SKWindow)[\"SimpleKeyboardInstances\"] = {};\n\n    this.currentInstanceName = this.utilities.camelCase(this.keyboardDOMClass);\n    (window as SKWindow)[\"SimpleKeyboardInstances\"][this.currentInstanceName] = this;\n\n    /**\n     * Instance vars\n     */\n    this.allKeyboardInstances = (window as SKWindow)[\"SimpleKeyboardInstances\"];\n    this.keyboardInstanceNames = Object.keys((window as SKWindow)[\"SimpleKeyboardInstances\"]);\n    this.isFirstKeyboardInstance =\n      this.keyboardInstanceNames[0] === this.currentInstanceName;\n\n    /**\n     * Physical Keyboard support\n     */\n    this.physicalKeyboard = new PhysicalKeyboard({\n      dispatch: this.dispatch,\n      getOptions: this.getOptions,\n    });\n\n    /**\n     * Initializing CandidateBox\n     */\n    this.candidateBox = this.options.enableLayoutCandidates\n      ? new CandidateBox({\n          utilities: this.utilities,\n          options: this.options,\n        })\n      : null;\n\n    /**\n     * Rendering keyboard\n     */\n    if (this.keyboardDOM) this.render();\n    else {\n      console.warn(`\".${keyboardDOMClass}\" was not found in the DOM.`);\n      throw new Error(\"KEYBOARD_DOM_ERROR\");\n    }\n\n    /**\n     * Modules\n     */\n    this.modules = {};\n    this.loadModules();\n  }\n\n  /**\n   * parseParams\n   */\n  handleParams = (\n    selectorOrOptions?: string | HTMLDivElement | KeyboardOptions,\n    keyboardOptions?: KeyboardOptions\n  ): {\n    keyboardDOMClass: string;\n    keyboardDOM: KeyboardElement;\n    options: Partial<KeyboardOptions | undefined>;\n  } => {\n    let keyboardDOMClass;\n    let keyboardDOM;\n    let options;\n\n    /**\n     * If first parameter is a string:\n     * Consider it as an element's class\n     */\n    if (typeof selectorOrOptions === \"string\") {\n      keyboardDOMClass = selectorOrOptions.split(\".\").join(\"\");\n      keyboardDOM = document.querySelector(\n        `.${keyboardDOMClass}`\n      ) as KeyboardElement;\n      options = keyboardOptions;\n\n      /**\n       * If first parameter is an KeyboardElement\n       * Consider it as the keyboard DOM element\n       */\n    } else if (selectorOrOptions instanceof HTMLDivElement) {\n      /**\n       * This element must have a class, otherwise throw\n       */\n      if (!selectorOrOptions.className) {\n        console.warn(\"Any DOM element passed as parameter must have a class.\");\n        throw new Error(\"KEYBOARD_DOM_CLASS_ERROR\");\n      }\n\n      keyboardDOMClass = selectorOrOptions.className.split(\" \")[0];\n      keyboardDOM = selectorOrOptions;\n      options = keyboardOptions;\n\n      /**\n       * Otherwise, search for .simple-keyboard DOM element\n       */\n    } else {\n      keyboardDOMClass = \"simple-keyboard\";\n      keyboardDOM = document.querySelector(\n        `.${keyboardDOMClass}`\n      ) as KeyboardElement;\n      options = selectorOrOptions;\n    }\n\n    return {\n      keyboardDOMClass,\n      keyboardDOM,\n      options,\n    };\n  };\n\n  /**\n   * Getters\n   */\n  getOptions = (): KeyboardOptions => this.options;\n  getCaretPosition = (): number | null => this.caretPosition;\n  getCaretPositionEnd = (): number | null => this.caretPositionEnd;\n\n  /**\n   * Changes the internal caret position\n   * @param {number} position The caret's start position\n   * @param {number} positionEnd The caret's end position\n   */\n  setCaretPosition(position: number | null, endPosition = position): void {\n    this.caretPosition = position;\n    this.caretPositionEnd = endPosition;\n  }\n\n  /**\n   * Retrieve the candidates for a given input\n   * @param input The input string to check\n   */\n  getInputCandidates(\n    input: string\n  ): { candidateKey: string; candidateValue: string } | Record<string, never> {\n    const {\n      layoutCandidates: layoutCandidatesObj,\n      layoutCandidatesCaseSensitiveMatch,\n    } = this.options;\n\n    if (!layoutCandidatesObj || typeof layoutCandidatesObj !== \"object\") {\n      return {};\n    }\n\n    const layoutCandidates = Object.keys(layoutCandidatesObj).filter(\n      (layoutCandidate: string) => {\n        const inputSubstr =\n          input.substring(0, this.getCaretPositionEnd() || 0) || input;\n        const regexp = new RegExp(\n          `${this.utilities.escapeRegex(layoutCandidate)}$`,\n          layoutCandidatesCaseSensitiveMatch ? \"g\" : \"gi\"\n        );\n        const matches = [...inputSubstr.matchAll(regexp)];\n        return !!matches.length;\n      }\n    );\n\n    if (layoutCandidates.length > 1) {\n      const candidateKey = layoutCandidates.sort(\n        (a, b) => b.length - a.length\n      )[0];\n      return {\n        candidateKey,\n        candidateValue: layoutCandidatesObj[candidateKey],\n      };\n    } else if (layoutCandidates.length) {\n      const candidateKey = layoutCandidates[0];\n      return {\n        candidateKey,\n        candidateValue: layoutCandidatesObj[candidateKey],\n      };\n    } else {\n      return {};\n    }\n  }\n\n  /**\n   * Shows a suggestion box with a list of candidate words\n   * @param candidates The chosen candidates string as defined in the layoutCandidates option\n   * @param targetElement The element next to which the candidates box will be shown\n   */\n  showCandidatesBox(\n    candidateKey: string,\n    candidateValue: string,\n    targetElement: KeyboardElement\n  ): void {\n    if (this.candidateBox) {\n      this.candidateBox.show({\n        candidateValue,\n        targetElement,\n        onSelect: (selectedCandidate: string, e: MouseEvent) => {\n          const {\n            layoutCandidatesCaseSensitiveMatch,\n            disableCandidateNormalization,\n            enableLayoutCandidatesKeyPress\n          } = this.options;\n\n          let candidateStr = selectedCandidate;\n\n          if(!disableCandidateNormalization) {\n            /**\n             * Making sure that our suggestions are not composed characters\n             */\n            candidateStr = selectedCandidate.normalize(\"NFD\");\n          }\n\n          /**\n           * Perform an action before any input change\n           */\n          if (typeof this.options.beforeInputUpdate === \"function\") {\n            this.options.beforeInputUpdate(this);\n          }\n\n          const currentInput = this.getInput(this.options.inputName, true);\n          const initialCaretPosition = this.getCaretPositionEnd() || 0;\n          const inputSubstr =\n            currentInput.substring(0, initialCaretPosition || 0) ||\n            currentInput;\n\n          const regexp = new RegExp(\n            `${this.utilities.escapeRegex(candidateKey)}$`,\n            layoutCandidatesCaseSensitiveMatch ? \"g\" : \"gi\"\n          );\n          const newInputSubstr = inputSubstr.replace(\n            regexp,\n            candidateStr\n          );\n          const newInput = currentInput.replace(inputSubstr, newInputSubstr);\n\n          const caretPositionDiff = newInputSubstr.length - inputSubstr.length;\n          let newCaretPosition =\n            (initialCaretPosition || currentInput.length) + caretPositionDiff;\n\n          if (newCaretPosition < 0) newCaretPosition = 0;\n\n          this.setInput(newInput, this.options.inputName, true);\n          this.setCaretPosition(newCaretPosition);\n\n          /**\n           * Calling onKeyPress\n           * We pass in the composed candidate instead of the decomposed one\n           * To prevent confusion for users\n           */\n          if (enableLayoutCandidatesKeyPress && typeof this.options.onKeyPress === \"function\")\n            this.options.onKeyPress(selectedCandidate, e);\n\n          if (typeof this.options.onChange === \"function\")\n            this.options.onChange(\n              this.getInput(this.options.inputName, true),\n              e\n            );\n\n          /**\n           * Calling onChangeAll\n           */\n          if (typeof this.options.onChangeAll === \"function\")\n            this.options.onChangeAll(this.getAllInputs(), e);\n        },\n      });\n    }\n  }\n\n  /**\n   * Handles clicks made to keyboard buttons\n   * @param  {string} button The button's layout name.\n   */\n  handleButtonClicked(button: string, e?: KeyboardHandlerEvent): void {\n    const { inputName = this.defaultName, debug } = this.options;\n    /**\n     * Ignoring placeholder buttons\n     */\n    if (button === \"{//}\") return;\n\n    /**\n     * Creating inputName if it doesn't exist\n     */\n    if (!this.input[inputName]) this.input[inputName] = \"\";\n\n    /**\n     * Perform an action before any input change\n     */\n    if (typeof this.options.beforeInputUpdate === \"function\") {\n      this.options.beforeInputUpdate(this);\n    }\n\n    /**\n     * Calculating new input\n     */\n    const updatedInput = this.utilities.getUpdatedInput(\n      button,\n      this.input[inputName],\n      this.caretPosition,\n      this.caretPositionEnd\n    );\n\n    /**\n     * EDGE CASE: Check for whole input selection changes that will yield same updatedInput\n     */\n    if (this.utilities.isStandardButton(button) && this.activeInputElement) {\n      const isEntireInputSelection =\n        this.input[inputName] &&\n        this.input[inputName] === updatedInput &&\n        this.caretPosition === 0 &&\n        this.caretPositionEnd === updatedInput.length;\n\n      if (isEntireInputSelection) {\n        this.setInput(\"\", this.options.inputName, true);\n        this.setCaretPosition(0);\n        this.activeInputElement.value = \"\";\n        this.activeInputElement.setSelectionRange(0, 0);\n        this.handleButtonClicked(button, e);\n        return;\n      }\n    }\n\n    /**\n     * Calling onKeyPress\n     */\n    if (typeof this.options.onKeyPress === \"function\")\n      this.options.onKeyPress(button, e);\n\n    if (\n      // If input will change as a result of this button press\n      this.input[inputName] !== updatedInput &&\n      // This pertains to the \"inputPattern\" option:\n      // If inputPattern isn't set\n      (!this.options.inputPattern ||\n        // Or, if it is set and if the pattern is valid - we proceed.\n        (this.options.inputPattern && this.inputPatternIsValid(updatedInput)))\n    ) {\n      /**\n       * If maxLength and handleMaxLength yield true, halting\n       */\n      if (\n        this.options.maxLength &&\n        this.utilities.handleMaxLength(this.input, updatedInput)\n      ) {\n        return;\n      }\n\n      /**\n       * Updating input\n       */\n      const newInputValue = this.utilities.getUpdatedInput(\n        button,\n        this.input[inputName],\n        this.caretPosition,\n        this.caretPositionEnd,\n        true\n      );\n\n      this.setInput(newInputValue, this.options.inputName, true);\n\n      if (debug) console.log(\"Input changed:\", this.getAllInputs());\n\n      if (this.options.debug) {\n        console.log(\n          \"Caret at: \",\n          this.getCaretPosition(),\n          this.getCaretPositionEnd(),\n          `(${this.keyboardDOMClass})`,\n          e?.type\n        );\n      }\n\n      /**\n       * Enforce syncInstanceInputs, if set\n       */\n      if (this.options.syncInstanceInputs) this.syncInstanceInputs();\n\n      /**\n       * Calling onChange\n       */\n      if (typeof this.options.onChange === \"function\")\n        this.options.onChange(this.getInput(this.options.inputName, true), e);\n\n      /**\n       * Calling onChangeAll\n       */\n      if (typeof this.options.onChangeAll === \"function\")\n        this.options.onChangeAll(this.getAllInputs(), e);\n\n      /**\n       * Check if this new input has candidates (suggested words)\n       */\n      if (e?.target && this.options.enableLayoutCandidates) {\n        const { candidateKey, candidateValue } =\n          this.getInputCandidates(updatedInput);\n\n        if (candidateKey && candidateValue) {\n          this.showCandidatesBox(\n            candidateKey,\n            candidateValue,\n            this.keyboardDOM\n          );\n        } else {\n          this.candidateBox?.destroy();\n        }\n      }\n    }\n\n    /**\n     * After a button is clicked the selection (if any) will disappear\n     * we should reflect this in our state, as applicable\n     */\n    if(this.caretPositionEnd && this.caretPosition !== this.caretPositionEnd){\n      this.setCaretPosition(this.caretPositionEnd, this.caretPositionEnd);\n\n      if(this.activeInputElement){\n        this.activeInputElement.setSelectionRange(this.caretPositionEnd, this.caretPositionEnd);\n      }\n      \n      if(this.options.debug){\n        console.log(\"Caret position aligned\", this.caretPosition);\n      }\n    }\n\n    if (debug) {\n      console.log(\"Key pressed:\", button);\n    }\n  }\n\n  /**\n   * Get mouse hold state\n   */\n  getMouseHold() {\n    return this.isMouseHold;\n  }\n\n  /**\n   * Mark mouse hold state as set\n   */\n  setMouseHold(value: boolean) {\n    if (this.options.syncInstanceInputs) {\n      this.dispatch((instance: SimpleKeyboard) => {\n        instance.isMouseHold = value;\n      });\n    } else {\n      this.isMouseHold = value;\n    }\n  }\n\n  /**\n   * Handles button mousedown\n   */\n  /* istanbul ignore next */\n  handleButtonMouseDown(button: string, e: KeyboardHandlerEvent): void {\n    if (e) {\n      /**\n       * Handle event options\n       */\n      if (this.options.preventMouseDownDefault) e.preventDefault();\n      if (this.options.stopMouseDownPropagation) e.stopPropagation();\n\n      /**\n       * Add active class\n       */\n      e.target.classList.add(this.activeButtonClass);\n    }\n\n    if (this.holdInteractionTimeout) clearTimeout(this.holdInteractionTimeout);\n    if (this.holdTimeout) clearTimeout(this.holdTimeout);\n\n    /**\n     * @type {boolean} Whether the mouse is being held onKeyPress\n     */\n    this.setMouseHold(true);\n\n    /**\n     * @type {object} Time to wait until a key hold is detected\n     */\n    if (!this.options.disableButtonHold) {\n      this.holdTimeout = window.setTimeout(() => {\n        if (\n          (this.getMouseHold() &&\n            // TODO: This needs to be configurable through options\n            ((!button.includes(\"{\") && !button.includes(\"}\")) ||\n              button === \"{delete}\" ||\n              button === \"{backspace}\" ||\n              button === \"{bksp}\" ||\n              button === \"{space}\" ||\n              button === \"{tab}\")) ||\n          button === \"{arrowright}\" ||\n          button === \"{arrowleft}\" ||\n          button === \"{arrowup}\" ||\n          button === \"{arrowdown}\"\n        ) {\n          if (this.options.debug) console.log(\"Button held:\", button);\n\n          this.handleButtonHold(button);\n        }\n        clearTimeout(this.holdTimeout);\n      }, 500);\n    }\n  }\n\n  /**\n   * Handles button mouseup\n   */\n  handleButtonMouseUp(button?: string, e?: KeyboardHandlerEvent): void {\n    if (e) {\n      /**\n       * Handle event options\n       */\n      if (this.options.preventMouseUpDefault && e.preventDefault)\n        e.preventDefault();\n      if (this.options.stopMouseUpPropagation && e.stopPropagation)\n        e.stopPropagation();\n\n      /* istanbul ignore next */\n      const isKeyboard =\n        e.target === this.keyboardDOM ||\n        (e.target && this.keyboardDOM.contains(e.target)) ||\n        (this.candidateBox &&\n          this.candidateBox.candidateBoxElement &&\n          (e.target === this.candidateBox.candidateBoxElement ||\n            (e.target &&\n              this.candidateBox.candidateBoxElement.contains(e.target))));\n\n      /**\n       * On click outside, remove candidateBox\n       */\n      if (!isKeyboard && this.candidateBox) {\n        this.candidateBox.destroy();\n      }\n    }\n\n    /**\n     * Remove active class\n     */\n    this.recurseButtons((buttonElement: Element) => {\n      buttonElement.classList.remove(this.activeButtonClass);\n    });\n\n    this.setMouseHold(false);\n    if (this.holdInteractionTimeout) clearTimeout(this.holdInteractionTimeout);\n\n    /**\n     * Calling onKeyReleased\n     */\n    if (button && typeof this.options.onKeyReleased === \"function\")\n      this.options.onKeyReleased(button, e);\n  }\n\n  /**\n   * Handles container mousedown\n   */\n  handleKeyboardContainerMouseDown(e: KeyboardHandlerEvent): void {\n    /**\n     * Handle event options\n     */\n    if (this.options.preventMouseDownDefault) e.preventDefault();\n  }\n\n  /**\n   * Handles button hold\n   */\n  /* istanbul ignore next */\n  handleButtonHold(button: string): void {\n    if (this.holdInteractionTimeout) clearTimeout(this.holdInteractionTimeout);\n\n    /**\n     * @type {object} Timeout dictating the speed of key hold iterations\n     */\n    this.holdInteractionTimeout = window.setTimeout(() => {\n      if (this.getMouseHold()) {\n        this.handleButtonClicked(button);\n        this.handleButtonHold(button);\n      } else {\n        clearTimeout(this.holdInteractionTimeout);\n      }\n    }, 100);\n  }\n\n  /**\n   * Send a command to all simple-keyboard instances (if you have several instances).\n   */\n  syncInstanceInputs(): void {\n    this.dispatch((instance: SimpleKeyboard) => {\n      instance.replaceInput(this.input);\n      instance.setCaretPosition(this.caretPosition, this.caretPositionEnd);\n    });\n  }\n\n  /**\n   * Clear the keyboard’s input.\n   * @param {string} [inputName] optional - the internal input to select\n   */\n  clearInput(\n    inputName: string = this.options.inputName || this.defaultName\n  ): void {\n    this.input[inputName] = \"\";\n\n    /**\n     * Reset caretPosition\n     */\n    this.setCaretPosition(0);\n\n    /**\n     * Enforce syncInstanceInputs, if set\n     */\n    if (this.options.syncInstanceInputs) this.syncInstanceInputs();\n  }\n\n  /**\n   * Get the keyboard’s input (You can also get it from the onChange prop).\n   * @param  {string} [inputName] optional - the internal input to select\n   */\n  getInput(\n    inputName: string = this.options.inputName || this.defaultName,\n    skipSync = false\n  ): string {\n    /**\n     * Enforce syncInstanceInputs, if set\n     */\n    if (this.options.syncInstanceInputs && !skipSync) this.syncInstanceInputs();\n\n    if (this.options.rtl) {\n      // Remove existing control chars\n      const inputWithoutRTLControl = this.input[inputName]\n        .replace(\"\\u202B\", \"\")\n        .replace(\"\\u202C\", \"\");\n\n      return \"\\u202B\" + inputWithoutRTLControl + \"\\u202C\";\n    } else {\n      return this.input[inputName];\n    }\n  }\n\n  /**\n   * Get all simple-keyboard inputs\n   */\n  getAllInputs(): KeyboardInput {\n    const output = {} as KeyboardInput;\n    const inputNames = Object.keys(this.input);\n\n    inputNames.forEach((inputName) => {\n      output[inputName] = this.getInput(inputName, true);\n    });\n\n    return output;\n  }\n\n  /**\n   * Set the keyboard’s input.\n   * @param  {string} input the input value\n   * @param  {string} inputName optional - the internal input to select\n   */\n  setInput(\n    input: string,\n    inputName: string = this.options.inputName || this.defaultName,\n    skipSync?: boolean\n  ): void {\n    this.input[inputName] = input;\n\n    /**\n     * Enforce syncInstanceInputs, if set\n     */\n    if (!skipSync && this.options.syncInstanceInputs) this.syncInstanceInputs();\n  }\n\n  /**\n   * Replace the input object (`keyboard.input`)\n   * @param  {object} inputObj The input object\n   */\n  replaceInput(inputObj: KeyboardInput): void {\n    this.input = inputObj;\n  }\n\n  /**\n   * Set new option or modify existing ones after initialization.\n   * @param  {object} options The options to set\n   */\n  setOptions(options = {}): void {\n    const changedOptions = this.changedOptions(options);\n    this.options = Object.assign(this.options, options);\n\n    if (changedOptions.length) {\n      if (this.options.debug) {\n        console.log(\"changedOptions\", changedOptions);\n      }\n\n      /**\n       * Some option changes require adjustments before re-render\n       */\n      this.onSetOptions(changedOptions);\n\n      /**\n       * Rendering\n       */\n      this.render();\n    }\n  }\n\n  /**\n   * Detecting changes to non-function options\n   * This allows us to ascertain whether a button re-render is needed\n   */\n  changedOptions(newOptions: Partial<KeyboardOptions>): string[] {\n    return Object.keys(newOptions).filter(\n      (optionName) =>\n        JSON.stringify(newOptions[optionName]) !==\n        JSON.stringify(this.options[optionName])\n    );\n  }\n\n  /**\n   * Executing actions depending on changed options\n   * @param  {object} options The options to set\n   */\n  onSetOptions(changedOptions: string[] = []): void {\n    /**\n     * Changed: layoutName\n     */\n    if (changedOptions.includes(\"layoutName\")) {\n      /**\n       * Reset candidateBox\n       */\n      if (this.candidateBox) {\n        this.candidateBox.destroy();\n      }\n    }\n\n    /**\n     * Changed: layoutCandidatesPageSize, layoutCandidates\n     */\n    if (\n      changedOptions.includes(\"layoutCandidatesPageSize\") ||\n      changedOptions.includes(\"layoutCandidates\")\n    ) {\n      /**\n       * Reset and recreate candidateBox\n       */\n      if (this.candidateBox) {\n        this.candidateBox.destroy();\n        this.candidateBox = new CandidateBox({\n          utilities: this.utilities,\n          options: this.options,\n        });\n      }\n    }\n  }\n\n  /**\n   * Remove all keyboard rows and reset keyboard values.\n   * Used internally between re-renders.\n   */\n  resetRows(): void {\n    if (this.keyboardRowsDOM) {\n      this.keyboardRowsDOM.remove();\n    }\n\n    this.keyboardDOM.className = this.keyboardDOMClass;\n    this.keyboardDOM.setAttribute(\"data-skInstance\", this.currentInstanceName);\n    this.buttonElements = {};\n  }\n\n  /**\n   * Send a command to all simple-keyboard instances at once (if you have multiple instances).\n   * @param  {function(instance: object, key: string)} callback Function to run on every instance\n   */\n  // eslint-disable-next-line no-unused-vars\n  dispatch(callback: (instance: SimpleKeyboard, key?: string) => void): void {\n    if (!(window as SKWindow)[\"SimpleKeyboardInstances\"]) {\n      console.warn(\n        `SimpleKeyboardInstances is not defined. Dispatch cannot be called.`\n      );\n      throw new Error(\"INSTANCES_VAR_ERROR\");\n    }\n\n    return Object.keys((window as SKWindow)[\"SimpleKeyboardInstances\"]).forEach((key) => {\n      callback((window as SKWindow)[\"SimpleKeyboardInstances\"][key], key);\n    });\n  }\n\n  /**\n   * Adds/Modifies an entry to the `buttonTheme`. Basically a way to add a class to a button.\n   * @param  {string} buttons List of buttons to select (separated by a space).\n   * @param  {string} className Classes to give to the selected buttons (separated by space).\n   */\n  addButtonTheme(buttons: string, className: string): void {\n    if (!className || !buttons) return;\n\n    buttons.split(\" \").forEach((button) => {\n      className.split(\" \").forEach((classNameItem) => {\n        if (!this.options.buttonTheme) this.options.buttonTheme = [];\n\n        let classNameFound = false;\n\n        /**\n         * If class is already defined, we add button to class definition\n         */\n        this.options.buttonTheme.map((buttonTheme) => {\n          if (buttonTheme?.class.split(\" \").includes(classNameItem)) {\n            classNameFound = true;\n\n            const buttonThemeArray = buttonTheme.buttons.split(\" \");\n            if (!buttonThemeArray.includes(button)) {\n              classNameFound = true;\n              buttonThemeArray.push(button);\n              buttonTheme.buttons = buttonThemeArray.join(\" \");\n            }\n          }\n          return buttonTheme;\n        });\n\n        /**\n         * If class is not defined, we create a new entry\n         */\n        if (!classNameFound) {\n          this.options.buttonTheme.push({\n            class: classNameItem,\n            buttons: buttons,\n          });\n        }\n      });\n    });\n\n    this.render();\n  }\n\n  /**\n   * Removes/Amends an entry to the `buttonTheme`. Basically a way to remove a class previously added to a button through buttonTheme or addButtonTheme.\n   * @param  {string} buttons List of buttons to select (separated by a space).\n   * @param  {string} className Classes to give to the selected buttons (separated by space).\n   */\n  removeButtonTheme(buttons: string, className: string): void {\n    /**\n     * When called with empty parameters, remove all button themes\n     */\n    if (!buttons && !className) {\n      this.options.buttonTheme = [];\n      this.render();\n      return;\n    }\n\n    /**\n     * If buttons are passed and buttonTheme has items\n     */\n    if (\n      buttons &&\n      Array.isArray(this.options.buttonTheme) &&\n      this.options.buttonTheme.length\n    ) {\n      const buttonArray = buttons.split(\" \");\n      buttonArray.forEach((button) => {\n        this.options?.buttonTheme?.map((buttonTheme, index) => {\n          /**\n           * If className is set, we affect the buttons only for that class\n           * Otherwise, we afect all classes\n           */\n          if (\n            (buttonTheme &&\n              className &&\n              className.includes(buttonTheme.class)) ||\n            !className\n          ) {\n            const filteredButtonArray = buttonTheme?.buttons\n              .split(\" \")\n              .filter((item) => item !== button);\n\n            /**\n             * If buttons left, return them, otherwise, remove button Theme\n             */\n            if (buttonTheme && filteredButtonArray?.length) {\n              buttonTheme.buttons = filteredButtonArray.join(\" \");\n            } else {\n              this.options.buttonTheme?.splice(index, 1);\n              buttonTheme = null;\n            }\n          }\n\n          return buttonTheme;\n        });\n      });\n\n      this.render();\n    }\n  }\n\n  /**\n   * Get the DOM Element of a button. If there are several buttons with the same name, an array of the DOM Elements is returned.\n   * @param  {string} button The button layout name to select\n   */\n  getButtonElement(\n    button: string\n  ): KeyboardElement | KeyboardElement[] | undefined {\n    let output;\n\n    const buttonArr = this.buttonElements[button];\n    if (buttonArr) {\n      if (buttonArr.length > 1) {\n        output = buttonArr;\n      } else {\n        output = buttonArr[0];\n      }\n    }\n\n    return output;\n  }\n\n  /**\n   * This handles the \"inputPattern\" option\n   * by checking if the provided inputPattern passes\n   */\n  inputPatternIsValid(inputVal: string): boolean {\n    const inputPatternRaw = this.options.inputPattern;\n    let inputPattern;\n\n    /**\n     * Check if input pattern is global or targeted to individual inputs\n     */\n    if (inputPatternRaw instanceof RegExp) {\n      inputPattern = inputPatternRaw;\n    } else {\n      inputPattern =\n        inputPatternRaw[this.options.inputName || this.defaultName];\n    }\n\n    if (inputPattern && inputVal) {\n      const didInputMatch = inputPattern.test(inputVal);\n\n      if (this.options.debug) {\n        console.log(\n          `inputPattern (\"${inputPattern}\"): ${\n            didInputMatch ? \"passed\" : \"did not pass!\"\n          }`\n        );\n      }\n\n      return didInputMatch;\n    } else {\n      /**\n       * inputPattern doesn't seem to be set for the current input, or input is empty. Pass.\n       */\n      return true;\n    }\n  }\n\n  /**\n   * Handles simple-keyboard event listeners\n   */\n  setEventListeners(): void {\n    /**\n     * Only first instance should set the event listeners\n     */\n    if (this.isFirstKeyboardInstance || !this.allKeyboardInstances) {\n      if (this.options.debug) {\n        console.log(`Caret handling started (${this.keyboardDOMClass})`);\n      }\n\n      const { physicalKeyboardHighlightPreventDefault = false } = this.options;\n\n      /**\n       * Event Listeners\n       */\n      document.addEventListener(\"keyup\", this.handleKeyUp, physicalKeyboardHighlightPreventDefault);\n      document.addEventListener(\"keydown\", this.handleKeyDown, physicalKeyboardHighlightPreventDefault);\n      document.addEventListener(\"mouseup\", this.handleMouseUp);\n      document.addEventListener(\"touchend\", this.handleTouchEnd);\n\n      if (this.options.updateCaretOnSelectionChange) {\n        document.addEventListener(\"selectionchange\", this.handleSelectionChange);\n      }\n\n      document.addEventListener(\"select\", this.handleSelect);\n    }\n  }\n\n  /**\n   * Event Handler: KeyUp\n   */\n  handleKeyUp(event: KeyboardHandlerEvent): void {\n    this.caretEventHandler(event);\n\n    if (this.options.physicalKeyboardHighlight) {\n      this.physicalKeyboard.handleHighlightKeyUp(event);\n    }\n  }\n\n  /**\n   * Event Handler: KeyDown\n   */\n  handleKeyDown(event: KeyboardHandlerEvent): void {\n    if (this.options.physicalKeyboardHighlight) {\n      this.physicalKeyboard.handleHighlightKeyDown(event);\n    }\n  }\n\n  /**\n   * Event Handler: MouseUp\n   */\n  handleMouseUp(event: KeyboardHandlerEvent): void {\n    this.caretEventHandler(event);\n  }\n\n  /**\n   * Event Handler: TouchEnd\n   */\n  /* istanbul ignore next */\n  handleTouchEnd(event: KeyboardHandlerEvent): void {\n    this.caretEventHandler(event);\n  }\n\n  /**\n   * Event Handler: Select\n   */\n  /* istanbul ignore next */\n  handleSelect(event: KeyboardHandlerEvent): void {\n    this.caretEventHandler(event);\n  }\n\n  /**\n   * Event Handler: SelectionChange\n   */\n  /* istanbul ignore next */\n  handleSelectionChange(event: KeyboardHandlerEvent): void {\n    /**\n     * Firefox is not reporting the correct caret position through this event\n     * https://github.com/hodgef/simple-keyboard/issues/1839\n     */\n    if(navigator.userAgent.includes('Firefox')){\n      return;\n    }\n    this.caretEventHandler(event);\n  }\n\n  /**\n   * Called by {@link setEventListeners} when an event that warrants a cursor position update is triggered\n   */\n  caretEventHandler(event: KeyboardHandlerEvent): void {\n    let targetTagName: string;\n    if (event.target.tagName) {\n      targetTagName = event.target.tagName.toLowerCase();\n    }\n\n    this.dispatch((instance) => {\n      let isKeyboard =\n        event.target === instance.keyboardDOM ||\n        (event.target && instance.keyboardDOM.contains(event.target));\n\n      /**\n       * If syncInstanceInputs option is enabled, make isKeyboard match any instance\n       * not just the current one\n       */\n      if (this.options.syncInstanceInputs && Array.isArray(event.path)) {\n        isKeyboard = event.path.some((item: HTMLElement) =>\n          item?.hasAttribute?.(\"data-skInstance\")\n        );\n      }\n\n      if (\n        (targetTagName === \"textarea\" ||\n          (targetTagName === \"input\" &&\n            [\"text\", \"search\", \"url\", \"tel\", \"password\"].includes(\n              event.target.type\n            ))) &&\n        !instance.options.disableCaretPositioning\n      ) {\n        /**\n         * Tracks current cursor position\n         * As keys are pressed, text will be added/removed at that position within the input.\n         */\n        let selectionStart = event.target.selectionStart;\n        let selectionEnd = event.target.selectionEnd;\n\n        if(instance.options.rtl){\n          selectionStart = instance.utilities.getRtlOffset(selectionStart, instance.getInput());\n          selectionEnd = instance.utilities.getRtlOffset(selectionEnd, instance.getInput());\n        }\n\n        instance.setCaretPosition(selectionStart, selectionEnd);\n\n        /**\n         * Tracking current input in order to handle caret positioning edge cases\n         */\n        instance.activeInputElement = event.target;\n\n        if (instance.options.debug) {\n          console.log(\n            \"Caret at: \",\n            instance.getCaretPosition(),\n            instance.getCaretPositionEnd(),\n            event && event.target.tagName.toLowerCase(),\n            `(${instance.keyboardDOMClass})`,\n            event?.type\n          );\n        }\n      } else if (\n        (instance.options.disableCaretPositioning || !isKeyboard) &&\n        event?.type !== \"selectionchange\"\n      ) {\n        /**\n         * If we toggled off disableCaretPositioning, we must ensure caretPosition doesn't persist once reactivated.\n         */\n        instance.setCaretPosition(null);\n\n        /**\n         * Resetting activeInputElement\n         */\n        instance.activeInputElement = null;\n\n        if (instance.options.debug) {\n          console.log(\n            `Caret position reset due to \"${event?.type}\" event`,\n            event\n          );\n        }\n      }\n    });\n  }\n\n  /**\n   * Execute an operation on each button\n   */\n  recurseButtons(fn: any): void {\n    if (!fn) return;\n\n    Object.keys(this.buttonElements).forEach((buttonName) =>\n      this.buttonElements[buttonName].forEach(fn)\n    );\n  }\n\n  /**\n   * Destroy keyboard listeners and DOM elements\n   */\n  destroy(): void {\n    if (this.options.debug)\n      console.log(\n        `Destroying simple-keyboard instance: ${this.currentInstanceName}`\n      );\n\n    const { physicalKeyboardHighlightPreventDefault = false } = this.options;\n\n    /**\n     * Remove document listeners\n     */\n    document.removeEventListener(\"keyup\", this.handleKeyUp, physicalKeyboardHighlightPreventDefault);\n    document.removeEventListener(\"keydown\", this.handleKeyDown, physicalKeyboardHighlightPreventDefault);\n    document.removeEventListener(\"mouseup\", this.handleMouseUp);\n    document.removeEventListener(\"touchend\", this.handleTouchEnd);\n    document.removeEventListener(\"select\", this.handleSelect);\n\n    // selectionchange is causing caret update issues on Chrome\n    // https://github.com/hodgef/simple-keyboard/issues/2346\n    if (this.options.updateCaretOnSelectionChange) {\n      document.removeEventListener(\"selectionchange\", this.handleSelectionChange);\n    }\n\n    document.onpointerup = null;\n    document.ontouchend = null;\n    document.ontouchcancel = null;\n    document.onmouseup = null;\n\n    /**\n     * Remove buttons\n     */\n    const deleteButton = (buttonElement: KeyboardElement | null) => {\n      if (buttonElement) {\n        buttonElement.onpointerdown = null;\n        buttonElement.onpointerup = null;\n        buttonElement.onpointercancel = null;\n        buttonElement.ontouchstart = null;\n        buttonElement.ontouchend = null;\n        buttonElement.ontouchcancel = null;\n        buttonElement.onclick = null;\n        buttonElement.onmousedown = null;\n        buttonElement.onmouseup = null;\n\n        buttonElement.remove();\n        buttonElement = null;\n      }\n    };\n\n    this.recurseButtons(deleteButton);\n\n    /**\n     * Remove wrapper events\n     */\n    this.keyboardDOM.onpointerdown = null;\n    this.keyboardDOM.ontouchstart = null;\n    this.keyboardDOM.onmousedown = null;\n\n    /**\n     * Clearing keyboard rows\n     */\n    this.resetRows();\n\n    /**\n     * Candidate box\n     */\n    if (this.candidateBox) {\n      this.candidateBox.destroy();\n      this.candidateBox = null;\n    }\n\n    /**\n     * Clearing activeInputElement\n     */\n    this.activeInputElement = null;\n\n    /**\n     * Removing instance attribute\n     */\n    this.keyboardDOM.removeAttribute(\"data-skInstance\");\n\n    /**\n     * Clearing keyboardDOM\n     */\n    this.keyboardDOM.innerHTML = \"\";\n\n    /**\n     * Remove instance\n     */\n    (window as SKWindow)[\"SimpleKeyboardInstances\"][this.currentInstanceName] = null;\n    delete (window as SKWindow)[\"SimpleKeyboardInstances\"][this.currentInstanceName];\n\n    /**\n     * Reset initialized flag\n     */\n    this.initialized = false;\n  }\n\n  /**\n   * Process buttonTheme option\n   */\n  getButtonThemeClasses(button: string): string[] {\n    const buttonTheme = this.options.buttonTheme;\n    let buttonClasses: string[] = [];\n\n    if (Array.isArray(buttonTheme)) {\n      buttonTheme.forEach((themeObj) => {\n        if (\n          themeObj &&\n          themeObj.class &&\n          typeof themeObj.class === \"string\" &&\n          themeObj.buttons &&\n          typeof themeObj.buttons === \"string\"\n        ) {\n          const themeObjClasses = themeObj.class.split(\" \");\n          const themeObjButtons = themeObj.buttons.split(\" \");\n\n          if (themeObjButtons.includes(button)) {\n            buttonClasses = [...buttonClasses, ...themeObjClasses];\n          }\n        } else {\n          console.warn(\n            `Incorrect \"buttonTheme\". Please check the documentation.`,\n            themeObj\n          );\n        }\n      });\n    }\n\n    return buttonClasses;\n  }\n\n  /**\n   * Process buttonAttributes option\n   */\n  setDOMButtonAttributes(button: string, callback: any): void {\n    const buttonAttributes = this.options.buttonAttributes;\n\n    if (Array.isArray(buttonAttributes)) {\n      buttonAttributes.forEach((attrObj) => {\n        if (\n          attrObj.attribute &&\n          typeof attrObj.attribute === \"string\" &&\n          attrObj.value &&\n          typeof attrObj.value === \"string\" &&\n          attrObj.buttons &&\n          typeof attrObj.buttons === \"string\"\n        ) {\n          const attrObjButtons = attrObj.buttons.split(\" \");\n\n          if (attrObjButtons.includes(button)) {\n            callback(attrObj.attribute, attrObj.value);\n          }\n        } else {\n          console.warn(\n            `Incorrect \"buttonAttributes\". Please check the documentation.`,\n            attrObj\n          );\n        }\n      });\n    }\n  }\n\n  onTouchDeviceDetected() {\n    /**\n     * Processing autoTouchEvents\n     */\n    this.processAutoTouchEvents();\n\n    /**\n     * Disabling contextual window on touch devices\n     */\n    this.disableContextualWindow();\n  }\n\n  /**\n   * Disabling contextual window for hg-button\n   */\n  /* istanbul ignore next */\n  disableContextualWindow() {\n    window.oncontextmenu = (event: KeyboardHandlerEvent) => {\n      if (event.target.classList?.contains(\"hg-button\")) {\n        event.preventDefault();\n        event.stopPropagation();\n        return false;\n      }\n    };\n  }\n\n  /**\n   * Process autoTouchEvents option\n   */\n  processAutoTouchEvents() {\n    if (this.options.autoUseTouchEvents) {\n      this.options.useTouchEvents = true;\n\n      if (this.options.debug) {\n        console.log(\n          `autoUseTouchEvents: Touch device detected, useTouchEvents enabled.`\n        );\n      }\n    }\n  }\n\n  /**\n   * Executes the callback function once simple-keyboard is rendered for the first time (on initialization).\n   */\n  onInit() {\n    if (this.options.debug) {\n      console.log(`${this.keyboardDOMClass} Initialized`);\n    }\n\n    /**\n     * setEventListeners\n     */\n    this.setEventListeners();\n\n    if (typeof this.options.onInit === \"function\") this.options.onInit(this);\n  }\n\n  /**\n   * Executes the callback function before a simple-keyboard render.\n   */\n  beforeFirstRender() {\n    /**\n     * Performing actions when touch device detected\n     */\n    if (this.utilities.isTouchDevice()) {\n      this.onTouchDeviceDetected();\n    }\n\n    if (typeof this.options.beforeFirstRender === \"function\")\n      this.options.beforeFirstRender(this);\n\n    /**\n     * Notify about PointerEvents usage\n     */\n    if (\n      this.isFirstKeyboardInstance &&\n      this.utilities.pointerEventsSupported() &&\n      !this.options.useTouchEvents &&\n      !this.options.useMouseEvents\n    ) {\n      if (this.options.debug) {\n        console.log(\"Using PointerEvents as it is supported by this browser\");\n      }\n    }\n\n    /**\n     * Notify about touch events usage\n     */\n    if (this.options.useTouchEvents) {\n      if (this.options.debug) {\n        console.log(\n          \"useTouchEvents has been enabled. Only touch events will be used.\"\n        );\n      }\n    }\n  }\n\n  /**\n   * Executes the callback function before a simple-keyboard render.\n   */\n  beforeRender() {\n    if (typeof this.options.beforeRender === \"function\")\n      this.options.beforeRender(this);\n  }\n\n  /**\n   * Executes the callback function every time simple-keyboard is rendered (e.g: when you change layouts).\n   */\n  onRender() {\n    if (typeof this.options.onRender === \"function\")\n      this.options.onRender(this);\n  }\n\n  /**\n   * Executes the callback function once all modules have been loaded\n   */\n  onModulesLoaded() {\n    if (typeof this.options.onModulesLoaded === \"function\")\n      this.options.onModulesLoaded(this);\n  }\n\n  /**\n   * Register module\n   */\n  registerModule = (name: string, initCallback: any) => {\n    if (!this.modules[name]) this.modules[name] = {};\n\n    initCallback(this.modules[name]);\n  };\n\n  /**\n   * Load modules\n   */\n  loadModules() {\n    if (Array.isArray(this.options.modules)) {\n      this.options.modules.forEach((KeyboardModule) => {\n        const keyboardModule = this.utilities.isConstructor(KeyboardModule) ?\n          new KeyboardModule(this) : KeyboardModule(this);\n\n        keyboardModule.init && keyboardModule.init(this);\n      });\n\n      this.keyboardPluginClasses = \"modules-loaded\";\n\n      this.render();\n      this.onModulesLoaded();\n    }\n  }\n\n  /**\n   * Get module prop\n   */\n  getModuleProp(name: string, prop: string) {\n    if (!this.modules[name]) return false;\n\n    return this.modules[name][prop];\n  }\n\n  /**\n   * getModulesList\n   */\n  getModulesList() {\n    return Object.keys(this.modules);\n  }\n\n  /**\n   * Parse Row DOM containers\n   */\n  parseRowDOMContainers(\n    rowDOM: HTMLDivElement,\n    rowIndex: number,\n    containerStartIndexes: number[],\n    containerEndIndexes: number[]\n  ) {\n    const rowDOMArray = Array.from(rowDOM.children);\n    let removedElements = 0;\n\n    if (rowDOMArray.length) {\n      containerStartIndexes.forEach((startIndex, arrIndex) => {\n        const endIndex = containerEndIndexes[arrIndex];\n\n        /**\n         * If there exists a respective end index\n         * if end index comes after start index\n         */\n        if (!endIndex || !(endIndex > startIndex)) {\n          return false;\n        }\n\n        /**\n         * Updated startIndex, endIndex\n         * This is since the removal of buttons to place a single button container\n         * results in a modified array size\n         */\n        const updated_startIndex = startIndex - removedElements;\n        const updated_endIndex = endIndex - removedElements;\n\n        /**\n         * Create button container\n         */\n        const containerDOM = document.createElement(\"div\");\n        containerDOM.className += \"hg-button-container\";\n        const containerUID = `${this.options.layoutName}-r${rowIndex}c${arrIndex}`;\n        containerDOM.setAttribute(\"data-skUID\", containerUID);\n\n        /**\n         * Taking elements due to be inserted into container\n         */\n        const containedElements = rowDOMArray.splice(\n          updated_startIndex,\n          updated_endIndex - updated_startIndex + 1\n        );\n        removedElements += updated_endIndex - updated_startIndex;\n\n        /**\n         * Inserting elements to container\n         */\n        containedElements.forEach((element) =>\n          containerDOM.appendChild(element)\n        );\n\n        /**\n         * Adding container at correct position within rowDOMArray\n         */\n        rowDOMArray.splice(updated_startIndex, 0, containerDOM);\n\n        /**\n         * Clearing old rowDOM children structure\n         */\n        rowDOM.innerHTML = \"\";\n\n        /**\n         * Appending rowDOM new children list\n         */\n        rowDOMArray.forEach((element) => rowDOM.appendChild(element));\n\n        if (this.options.debug) {\n          console.log(\n            \"rowDOMContainer\",\n            containedElements,\n            updated_startIndex,\n            updated_endIndex,\n            removedElements + 1\n          );\n        }\n      });\n    }\n\n    return rowDOM;\n  }\n\n  /**\n   * getKeyboardClassString\n   */\n  getKeyboardClassString = (...baseDOMClasses: any[]) => {\n    const keyboardClasses = [this.keyboardDOMClass, ...baseDOMClasses].filter(\n      (DOMClass) => !!DOMClass\n    );\n\n    return keyboardClasses.join(\" \");\n  };\n\n  /**\n   * Renders rows and buttons as per options\n   */\n  render() {\n    /**\n     * Clear keyboard\n     */\n    this.resetRows();\n\n    /**\n     * Calling beforeFirstRender\n     */\n    if (!this.initialized) {\n      this.beforeFirstRender();\n    }\n\n    /**\n     * Calling beforeRender\n     */\n    this.beforeRender();\n\n    const layoutClass = `hg-layout-${this.options.layoutName}`;\n    const layout = this.options.layout || getDefaultLayout();\n    const useTouchEvents = this.options.useTouchEvents || false;\n    const useTouchEventsClass = useTouchEvents ? \"hg-touch-events\" : \"\";\n    const useMouseEvents = this.options.useMouseEvents || false;\n    const disableRowButtonContainers = this.options.disableRowButtonContainers;\n\n    /**\n     * Adding themeClass, layoutClass to keyboardDOM\n     */\n    this.keyboardDOM.className = this.getKeyboardClassString(\n      this.options.theme,\n      layoutClass,\n      this.keyboardPluginClasses,\n      useTouchEventsClass\n    );\n\n    /**\n     * Adding keyboard identifier\n     */\n    this.keyboardDOM.setAttribute(\"data-skInstance\", this.currentInstanceName);\n\n    /**\n     * Create row wrapper\n     */\n    this.keyboardRowsDOM = document.createElement(\"div\");\n    this.keyboardRowsDOM.className = \"hg-rows\";\n\n    /**\n     * Iterating through each row\n     */\n    layout[this.options.layoutName || this.defaultName].forEach(\n      (row: string, rIndex: number) => {\n        let rowArray = row.split(\" \");\n\n        /**\n         * Enforce excludeFromLayout\n         */\n        if (\n          this.options.excludeFromLayout &&\n          this.options.excludeFromLayout[\n            this.options.layoutName || this.defaultName\n          ]\n        ) {\n          rowArray = rowArray.filter(\n            (buttonName) =>\n              this.options.excludeFromLayout &&\n              !this.options.excludeFromLayout[\n                this.options.layoutName || this.defaultName\n              ].includes(buttonName)\n          );\n        }\n\n        /**\n         * Creating empty row\n         */\n        let rowDOM = document.createElement(\"div\");\n        rowDOM.className += \"hg-row\";\n\n        /**\n         * Tracking container indicators in rows\n         */\n        const containerStartIndexes: number[] = [];\n        const containerEndIndexes: number[] = [];\n\n        /**\n         * Iterating through each button in row\n         */\n        rowArray.forEach((button, bIndex) => {\n          /**\n           * Check if button has a container indicator\n           */\n          const buttonHasContainerStart =\n            !disableRowButtonContainers &&\n            typeof button === \"string\" &&\n            button.length > 1 &&\n            button.indexOf(\"[\") === 0;\n\n          const buttonHasContainerEnd =\n            !disableRowButtonContainers &&\n            typeof button === \"string\" &&\n            button.length > 1 &&\n            button.indexOf(\"]\") === button.length - 1;\n\n          /**\n           * Save container start index, if applicable\n           */\n          if (buttonHasContainerStart) {\n            containerStartIndexes.push(bIndex);\n\n            /**\n             * Removing indicator\n             */\n            button = button.replace(/\\[/g, \"\");\n          }\n\n          if (buttonHasContainerEnd) {\n            containerEndIndexes.push(bIndex);\n\n            /**\n             * Removing indicator\n             */\n            button = button.replace(/\\]/g, \"\");\n          }\n\n          /**\n           * Processing button options\n           */\n          const fctBtnClass = this.utilities.getButtonClass(button);\n          const buttonDisplayName = this.utilities.getButtonDisplayName(\n            button,\n            this.options.display,\n            this.options.mergeDisplay\n          );\n\n          /**\n           * Creating button\n           */\n          const buttonType = this.options.useButtonTag ? \"button\" : \"div\";\n          const buttonDOM = document.createElement(buttonType);\n          buttonDOM.className += `hg-button ${fctBtnClass}`;\n\n          /**\n           * Adding buttonTheme\n           */\n          buttonDOM.classList.add(...this.getButtonThemeClasses(button));\n\n          /**\n           * Adding buttonAttributes\n           */\n          this.setDOMButtonAttributes(\n            button,\n            (attribute: string, value: string) => {\n              buttonDOM.setAttribute(attribute, value);\n            }\n          );\n\n          this.activeButtonClass = \"hg-activeButton\";\n\n          /**\n           * Handle button click event\n           */\n          /* istanbul ignore next */\n          if (\n            this.utilities.pointerEventsSupported() &&\n            !useTouchEvents &&\n            !useMouseEvents\n          ) {\n            /**\n             * Handle PointerEvents\n             */\n            buttonDOM.onpointerdown = (e: KeyboardHandlerEvent) => {\n              this.handleButtonClicked(button, e);\n              this.handleButtonMouseDown(button, e);\n            };\n            buttonDOM.onpointerup = (e: KeyboardHandlerEvent) => {\n              this.handleButtonMouseUp(button, e);\n            };\n            buttonDOM.onpointercancel = (e: KeyboardHandlerEvent) => {\n              this.handleButtonMouseUp(button, e);\n            };\n          } else {\n            /**\n             * Fallback for browsers not supporting PointerEvents\n             */\n            if (useTouchEvents) {\n              /**\n               * Handle touch events\n               */\n              buttonDOM.ontouchstart = (e: KeyboardHandlerEvent) => {\n                this.handleButtonClicked(button, e);\n                this.handleButtonMouseDown(button, e);\n              };\n              buttonDOM.ontouchend = (e: KeyboardHandlerEvent) => {\n                this.handleButtonMouseUp(button, e);\n              };\n              buttonDOM.ontouchcancel = (e: KeyboardHandlerEvent) => {\n                this.handleButtonMouseUp(button, e);\n              };\n            } else {\n              /**\n               * Handle mouse events\n               */\n              buttonDOM.onclick = (e: KeyboardHandlerEvent) => {\n                this.setMouseHold(false);\n                /**\n                 * Fire button handler in onclick for compatibility reasons\n                 * This fires handler before onKeyReleased, therefore when that option is set we will fire the handler\n                 * in onmousedown instead\n                 */\n                if (\n                  typeof this.options.onKeyReleased !== \"function\" &&\n                  !(this.options.useMouseEvents && this.options.clickOnMouseDown)\n                ) {\n                  this.handleButtonClicked(button, e);\n                }\n              };\n              buttonDOM.onmousedown = (e: KeyboardHandlerEvent) => {\n                /**\n                 * Fire button handler for onKeyReleased use-case\n                 */\n                if (\n                  (\n                    typeof this.options.onKeyReleased === \"function\" ||\n                    (this.options.useMouseEvents && this.options.clickOnMouseDown)\n                  ) &&\n                  !this.isMouseHold\n                ) {\n                  this.handleButtonClicked(button, e);\n                }\n                this.handleButtonMouseDown(button, e);\n              };\n              buttonDOM.onmouseup = (e: KeyboardHandlerEvent) => {\n                this.handleButtonMouseUp(button, e);\n              };\n            }\n          }\n\n          /**\n           * Adding identifier\n           */\n          buttonDOM.setAttribute(\"data-skBtn\", button);\n\n          /**\n           * Adding unique id\n           * Since there's no limit on spawning same buttons, the unique id ensures you can style every button\n           */\n          const buttonUID = `${this.options.layoutName}-r${rIndex}b${bIndex}`;\n          buttonDOM.setAttribute(\"data-skBtnUID\", buttonUID);\n\n          /**\n           * Adding button label to button\n           */\n          const buttonSpanDOM = document.createElement(\"span\");\n          buttonSpanDOM.innerHTML = buttonDisplayName;\n          buttonDOM.appendChild(buttonSpanDOM);\n\n          /**\n           * Adding to buttonElements\n           */\n          if (!this.buttonElements[button]) this.buttonElements[button] = [];\n\n          this.buttonElements[button].push(buttonDOM);\n\n          /**\n           * Appending button to row\n           */\n          rowDOM.appendChild(buttonDOM);\n        });\n\n        /**\n         * Parse containers in row\n         */\n        rowDOM = this.parseRowDOMContainers(\n          rowDOM,\n          rIndex,\n          containerStartIndexes,\n          containerEndIndexes\n        );\n\n        /**\n         * Appending row to hg-rows\n         */\n        this.keyboardRowsDOM.appendChild(rowDOM);\n      }\n    );\n\n    /**\n     * Appending row to keyboard\n     */\n    this.keyboardDOM.appendChild(this.keyboardRowsDOM);\n\n    /**\n     * Calling onRender\n     */\n    this.onRender();\n\n    if (!this.initialized) {\n      /**\n       * Ensures that onInit and beforeFirstRender are only called once per instantiation\n       */\n      this.initialized = true;\n\n      /**\n       * Handling parent events\n       */\n      /* istanbul ignore next */\n      if (\n        this.utilities.pointerEventsSupported() &&\n        !useTouchEvents &&\n        !useMouseEvents\n      ) {\n        document.onpointerup = (e: KeyboardHandlerEvent) =>\n          this.handleButtonMouseUp(undefined, e);\n        this.keyboardDOM.onpointerdown = (e: KeyboardHandlerEvent) =>\n          this.handleKeyboardContainerMouseDown(e);\n      } else if (useTouchEvents) {\n        /**\n         * Handling ontouchend, ontouchcancel\n         */\n        document.ontouchend = (e: KeyboardHandlerEvent) =>\n          this.handleButtonMouseUp(undefined, e);\n        document.ontouchcancel = (e: KeyboardHandlerEvent) =>\n          this.handleButtonMouseUp(undefined, e);\n\n        this.keyboardDOM.ontouchstart = (e: KeyboardHandlerEvent) =>\n          this.handleKeyboardContainerMouseDown(e);\n      } else if (!useTouchEvents) {\n        /**\n         * Handling mouseup\n         */\n        document.onmouseup = (e: KeyboardHandlerEvent) =>\n          this.handleButtonMouseUp(undefined, e);\n        this.keyboardDOM.onmousedown = (e: KeyboardHandlerEvent) =>\n          this.handleKeyboardContainerMouseDown(e);\n      }\n\n      /**\n       * Calling onInit\n       */\n      this.onInit();\n    }\n  }\n}\n\nexport default SimpleKeyboard;\n", "import { KeyboardLayoutObject } from \"../interfaces\";\n\nexport const getDefaultLayout = (): KeyboardLayoutObject => {\n  return {\n    default: [\n      \"` 1 2 3 4 5 6 7 8 9 0 - = {bksp}\",\n      \"{tab} q w e r t y u i o p [ ] \\\\\",\n      \"{lock} a s d f g h j k l ; ' {enter}\",\n      \"{shift} z x c v b n m , . / {shift}\",\n      \".com @ {space}\",\n    ],\n    shift: [\n      \"~ ! @ # $ % ^ & * ( ) _ + {bksp}\",\n      \"{tab} Q W E R T Y U I O P { } |\",\n      '{lock} A S D F G H J K L : \" {enter}',\n      \"{shift} Z X C V B N M < > ? {shift}\",\n      \".com @ {space}\",\n    ],\n  };\n};\n", "import SimpleKeyboard from \"./components/Keyboard\";\nexport { SimpleKeyboard };\nexport default SimpleKeyboard;\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "Utilities", "_ref", "getOptions", "getCaretPosition", "getCaretPositionEnd", "dispatch", "_classCallCheck", "_defineProperty", "button", "length", "bindMethods", "includes", "buttonTypeClass", "getButtonType", "buttonWithoutBraces", "replace", "buttonNormalized", "concat", "display", "arguments", "undefined", "assign", "getDefaultDiplay", "input", "caretPos", "caretPosEnd", "moveCaret", "options", "commonParams", "output", "removeAt", "apply", "removeForwardsAt", "addStringAt", "tabCharOnTab", "newLineOnEnter", "Number", "isInteger", "debug", "console", "log", "minus", "newCaretPos", "updateCaretPosAction", "instance", "setCaretPosition", "caretPosition", "source", "str", "position", "positionEnd", "slice", "join", "isMaxLengthReached", "updateCaretPos", "emojiMatchedReg", "substring", "match", "substr", "inputObj", "updatedInput", "max<PERSON><PERSON><PERSON>", "currentInput", "inputName", "condition", "max<PERSON><PERSON><PERSON>Reached", "_typeof", "Boolean", "window", "navigator", "maxTouchPoints", "PointerEvent", "toLowerCase", "trim", "split", "reduce", "word", "toUpperCase", "arr", "size", "_toConsumableArray", "Array", "Math", "ceil", "map", "_", "i", "index", "newIndex", "startMarkerIndex", "indexOf", "f", "Reflect", "construct", "String", "e", "myClass", "_step", "_iterator", "_createForOfIteratorHelper", "getOwnPropertyNames", "s", "n", "done", "myMethod", "bind", "err", "PhysicalKeyboard", "_this", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "code", "keyCodeToKey", "keyCode", "physicalKeyboardHighlightPreventDefault", "isModifierKey", "preventDefault", "stopImmediatePropagation", "buttonPressed", "getSimpleKeyboardLayoutKey", "buttonDOM", "buttonName", "standardButtonPressed", "getButtonElement", "functionButtonPressed", "_buttonDOM$", "_buttonDOM$$onpointer", "_buttonDOM", "_buttonDOM$onpointerd", "applyButtonStyle", "buttonElement", "style", "background", "physicalKeyboardHighlightBgColor", "color", "physicalKeyboardHighlightTextColor", "isArray", "for<PERSON>ach", "physicalKeyboardHighlightPress", "physicalKeyboardHighlightPressUsePointerEvents", "onpointerdown", "physicalKeyboardHighlightPressUseClick", "_buttonDOM$2", "click", "handleButtonClicked", "_buttonDOM$3", "_buttonDOM$3$onpointe", "_buttonDOM$onpointeru", "removeAttribute", "onpointerup", "_output", "keyId", "CandidateBox", "utilities", "pageSize", "layoutCandidatesPageSize", "candidateBoxElement", "remove", "pageIndex", "_ref2", "<PERSON><PERSON><PERSON><PERSON>", "targetElement", "onSelect", "candidateListPages", "chunkArray", "renderPage", "nbPages", "onItemSelected", "selectedCandidate", "destroy", "_ref3", "_this$candidateBoxEle", "_this2", "document", "createElement", "className", "candidateListULElement", "candidateListItem", "_this2$options$displa", "candidateListL<PERSON>lement", "getMouseEvent", "mouseEvent", "useTouchEvents", "TouchEvent", "MouseEvent", "innerHTML", "ontouchstart", "onclick", "append<PERSON><PERSON><PERSON>", "isPrevBtnElementActive", "prevBtnElement", "classList", "add", "prevBtnElementClickAction", "isNextBtnElementActive", "nextBtnElement", "nextBtnElementClickAction", "prepend", "SimpleKeyboard", "selectorOrOptions", "keyboardOptions", "keyboardDOMClass", "keyboardDOM", "querySelector", "HTMLDivElement", "warn", "Error", "caretPositionEnd", "name", "initCallback", "modules", "_len", "baseDOMClasses", "_key", "filter", "DOMClass", "_this$handleParams", "handleParams", "_this$handleParams$op", "_objectSpread", "layoutName", "theme", "preventMouseDownDefault", "enableLayoutCandidates", "excludeFromLayout", "keyboardPluginClasses", "_this$options$inputNa", "defaultName", "buttonElements", "currentInstanceName", "camelCase", "allKeyboardInstances", "keyboardInstanceNames", "keys", "isFirstKeyboardInstance", "physicalKeyboard", "candidate<PERSON><PERSON>", "render", "loadModules", "endPosition", "_this$options", "layoutCandidatesObj", "layoutCandidates", "layoutCandidatesCaseSensitiveMatch", "layoutCandidate", "inputSubstr", "regexp", "RegExp", "escapeRegex", "matchAll", "<PERSON><PERSON><PERSON>", "sort", "a", "b", "_this3", "show", "_this3$options", "disableCandidateNormalization", "enableLayoutCandidatesKeyPress", "candidateStr", "normalize", "beforeInputUpdate", "getInput", "initialCaretPosition", "newInputSubstr", "newInput", "caretPositionDiff", "newCaretPosition", "setInput", "onKeyPress", "onChange", "onChangeAll", "getAllInputs", "_this$options2", "_this$options2$inputN", "getUpdatedInput", "isStandardButton", "activeInputElement", "setSelectionRange", "inputPattern", "inputPatternIsValid", "handleMaxLength", "newInputValue", "type", "syncInstanceInputs", "target", "_this$candidateBox", "_this$getInputCandida", "getInputCandidates", "showCandidatesBox", "isMouseHold", "_this4", "stopMouseDownPropagation", "stopPropagation", "activeButtonClass", "holdInteractionTimeout", "clearTimeout", "holdTimeout", "setMouseHold", "disableButtonHold", "setTimeout", "getMouseHold", "handleButtonHold", "_this5", "preventMouseUpDefault", "stopMouseUpPropagation", "contains", "recurseButtons", "onKeyReleased", "_this6", "_this7", "replaceInput", "skipSync", "rtl", "_this8", "changedOptions", "onSetOptions", "newOptions", "_this9", "optionName", "JSON", "stringify", "keyboardRowsDOM", "setAttribute", "callback", "buttons", "_this0", "classNameItem", "buttonTheme", "classNameFound", "buttonThemeArray", "push", "class", "_this1", "_this1$options", "_buttonTheme", "_this1$options$button", "filteredButtonArray", "item", "splice", "buttonArr", "inputVal", "inputPatternRaw", "didInputMatch", "test", "_this$options$physica", "addEventListener", "handleKeyUp", "handleKeyDown", "handleMouseUp", "handleTouchEnd", "updateCaretOnSelectionChange", "handleSelectionChange", "handleSelect", "event", "caretEventHandler", "physicalKeyboardHighlight", "handleHighlightKeyUp", "handleHighlightKeyDown", "userAgent", "targetTagName", "_this10", "tagName", "isKeyboard", "path", "some", "_item$hasAttribute", "hasAttribute", "disableCaretPositioning", "selectionStart", "selectionEnd", "getRtlOffset", "fn", "_this11", "_this$options$physica2", "removeEventListener", "ontouchend", "ontouchcancel", "onmouseup", "onpointercancel", "onmousedown", "resetRows", "initialized", "buttonClasses", "themeObj", "themeObjClasses", "buttonAttributes", "attrObj", "attribute", "processAutoTouchEvents", "disableContextualWindow", "oncontextmenu", "_event$target$classLi", "autoUseTouchEvents", "setEventListeners", "onInit", "isTouchDevice", "onTouchDeviceDetected", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pointerEventsSupported", "useMouseEvents", "beforeRender", "onRender", "onModulesLoaded", "_this12", "KeyboardModule", "keyboardModule", "isConstructor", "init", "rowDOM", "rowIndex", "containerStartIndexes", "containerEndIndexes", "_this13", "rowDOMArray", "from", "children", "removedElements", "startIndex", "arrIndex", "endIndex", "updated_startIndex", "updated_endIndex", "containerDOM", "containerUID", "containedElements", "element", "_this14", "layoutClass", "layout", "default", "shift", "useTouchEventsClass", "disableRowButtonContainers", "getKeyboardClassString", "row", "rIndex", "rowArray", "bIndex", "_buttonDOM$classList", "buttonHasContainerStart", "buttonHasContainerEnd", "fctBtnClass", "getButtonClass", "buttonDisplayName", "getButtonDisplayName", "mergeDisplay", "buttonType", "useButtonTag", "getButtonThemeClasses", "setDOMButtonAttributes", "handleButtonMouseDown", "handleButtonMouseUp", "clickOnMouseDown", "buttonUID", "buttonSpanDOM", "parseRowDOMContainers", "handleKeyboardContainerMouseDown"], "sourceRoot": ""}