{"version": 3, "file": "index.modern.esm.js", "mappings": ";;;;;;;;;;;;;;;oiDAGA,IAGMA,EAAS,WA2Bb,O,EAjBA,SAAAA,EAAAC,GAKoB,IAJlBC,EAAUD,EAAVC,WACAC,EAAgBF,EAAhBE,iBACAC,EAAmBH,EAAnBG,oBACAC,EAAQJ,EAARI,U,4FAAQC,CAAA,KAAAN,GAAAO,EAAA,0BAAAA,EAAA,gCAAAA,EAAA,mCAAAA,EAAA,wBAAAA,EAAA,gCA6RVA,EAAA,yBAGmB,SAACC,GAAc,OAChCA,KAA0B,MAAdA,EAAO,IAA4C,MAA9BA,EAAOA,EAAOC,OAAS,GAAW,IA/RnEC,KAAKR,WAAaA,EAClBQ,KAAKP,iBAAmBA,EACxBO,KAAKN,oBAAsBA,EAC3BM,KAAKL,SAAWA,EAKhBL,EAAUW,YAAYX,EAAWU,KACnC,E,EAEA,EAAAE,IAAA,gBAAAC,MAMA,SAAcL,GACZ,OAAOA,EAAOM,SAAS,MAAQN,EAAOM,SAAS,MAAmB,SAAXN,EACnD,cACA,aACN,GAEA,CAAAI,IAAA,iBAAAC,MAMA,SAAeL,GACb,IAAMO,EAAkBL,KAAKM,cAAcR,GACrCS,EAAsBT,EAAOU,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAC7DC,EAAmB,GAKvB,MAHwB,gBAApBJ,IACFI,EAAmB,cAAHC,OAAiBH,IAE5B,MAAPG,OAAaL,GAAeK,OAAGD,EACjC,GAEA,CAAAP,IAAA,mBAAAC,MAGA,WACE,MAAO,CACL,SAAU,YACV,cAAe,YACf,UAAW,UACX,UAAW,QACX,cAAe,QACf,eAAgB,QAChB,QAAS,MACT,MAAO,QACP,QAAS,MACT,SAAU,OACV,aAAc,OACd,WAAY,SACZ,UAAW,IACX,OAAQ,IACR,QAAS,MACT,WAAY,MACZ,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,OAAQ,KACR,QAAS,MACT,QAAS,MACT,QAAS,MACT,iBAAkB,IAClB,YAAa,OACb,YAAa,IACb,cAAe,IACf,cAAe,IACf,eAAgB,IAChB,WAAY,QACZ,eAAgB,SAChB,UAAW,QACX,WAAY,MACZ,SAAU,OACV,WAAY,KACZ,WAAY,MACZ,kBAAmB,MACnB,QAAS,MACT,aAAc,OACd,mBAAoB,IACpB,mBAAoB,IACpB,cAAe,IACf,gBAAiB,QACjB,WAAY,IACZ,kBAAmB,IACnB,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IACb,YAAa,IAEjB,GACA,CAAAD,IAAA,uBAAAC,MAOA,SACEL,EACAa,GASA,OALEA,EAHUC,UAAAb,OAAA,QAAAc,IAAAD,UAAA,IAAAA,UAAA,GAGAE,OAAOC,OAAO,CAAC,EAAGf,KAAKgB,mBAAoBL,GAE3CA,GAAWX,KAAKgB,oBAGblB,IAAWA,CAC5B,GAEA,CAAAI,IAAA,kBAAAC,MASA,SACEL,EACAmB,EACAC,GAGA,IAFAC,EAAWP,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAGM,EACdE,EAASR,UAAAb,OAAA,QAAAc,IAAAD,UAAA,IAAAA,UAAA,GAEHS,EAAUrB,KAAKR,aACf8B,EAAkE,CACtEJ,EACAC,EACAC,GAGEG,EAASN,EAuDb,OApDc,WAAXnB,GAAkC,gBAAXA,IACxByB,EAAOxB,OAAS,EAEhBwB,EAASvB,KAAKwB,SAAQC,MAAbzB,KAAI,CAAUuB,GAAMb,OAAKY,KAEtB,aAAXxB,GAAoC,oBAAXA,IAC1ByB,EAAOxB,OAAS,EAEhBwB,EAASvB,KAAK0B,iBAAgBD,MAArBzB,KAAI,CAAkBuB,GAAMb,OAAKY,IACtB,YAAXxB,EACTyB,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CAAauB,EAAQ,KAAGb,OAAKY,IAE/B,UAAXxB,GAEkC,kBAAzBuB,EAAQO,eACU,IAAzBP,EAAQO,aAKE,YAAX9B,GAAmC,kBAAXA,IACzBuB,EAAQQ,eAIR/B,EAAOM,SAAS,WAChB0B,OAAOC,UAAUD,OAAOhC,EAAOA,EAAOC,OAAS,KAE/CwB,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CACXuB,EACAzB,EAAOA,EAAOC,OAAS,IAAEW,OACtBY,IAEe,mBAAXxB,EACTyB,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CAAauB,EAAQ,KAAGb,OAAKY,IACxB,qBAAXxB,EACPyB,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CAAauB,EAAQ,KAAGb,OAAKY,IACxB,qBAAXxB,EACPyB,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CAAauB,EAAQ,KAAGb,OAAKY,IACxB,gBAAXxB,EACPyB,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CAAauB,EAAQ,KAAGb,OAAKY,IACxB,oBAAXxB,EACPyB,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CAAauB,EAAQ,KAAGb,OAAKY,IACxB,MAAXxB,GAA6B,MAAXA,EACzByB,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CAAauB,EAAQzB,GAAMY,OAAKY,IACrCxB,EAAOM,SAAS,MAASN,EAAOM,SAAS,OACjDmB,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CAAauB,EAAQzB,GAAMY,OAAKY,KAvB7CC,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CAAauB,EAAQ,MAAIb,OAAKY,IAL3CC,EAASvB,KAAK2B,YAAWF,MAAhBzB,KAAI,CAAauB,EAAQ,MAAIb,OAAKY,IA8B1CD,EAAQW,OACTC,QAAQC,IAAI,kBAAmBX,GAG1BA,CACT,GAEA,CAAArB,IAAA,iBAAAC,MAMA,SAAeJ,GAA+B,IAAfoC,EAAKvB,UAAAb,OAAA,QAAAc,IAAAD,UAAA,IAAAA,UAAA,GAC5BwB,EAAcpC,KAAKqC,qBAAqBtC,EAAQoC,GAEtDnC,KAAKL,UAAS,SAAC2C,GACbA,EAASC,iBAAiBH,EAC5B,GACF,GAEA,CAAAlC,IAAA,uBAAAC,MAMA,SAAqBJ,GAA+B,IAAfoC,EAAKvB,UAAAb,OAAA,QAAAc,IAAAD,UAAA,IAAAA,UAAA,GAClCS,EAAUrB,KAAKR,aACjBgD,EAAgBxC,KAAKP,mBAczB,OAZqB,MAAjB+C,IACEL,EACEK,EAAgB,IAAGA,GAAgCzC,GAEvDyC,GAAgCzC,GAIhCsB,EAAQW,OACVC,QAAQC,IAAI,YAAaM,GAGpBA,CACT,GAEA,CAAAtC,IAAA,cAAAC,MAQA,SACEsC,EACAC,GAIA,IACInB,EAJJoB,EAAQ/B,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAG6B,EAAO1C,OAClB6C,EAAWhC,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAG6B,EAAO1C,OACrBqB,EAASR,UAAAb,OAAA,QAAAc,IAAAD,UAAA,IAAAA,UAAA,GAmBT,OAfK+B,GAAyB,IAAbA,GAGfpB,EAAS,CAACkB,EAAOI,MAAM,EAAGF,GAAWD,EAAKD,EAAOI,MAAMD,IAAcE,KACnE,IAMG9C,KAAK+C,sBACJ3B,GAAWpB,KAAKgD,eAAeN,EAAI3C,SAVzCwB,EAASkB,EAASC,EAcbnB,CACT,GAAC,CAAArB,IAAA,WAAAC,MAeD,SACEsC,GAIA,IAKIlB,EARJoB,EAAQ/B,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAG6B,EAAO1C,OAClB6C,EAAWhC,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAG6B,EAAO1C,OACrBqB,EAASR,UAAAb,OAAA,QAAAc,IAAAD,UAAA,IAAAA,UAAA,GAET,GAAiB,IAAb+B,GAAkC,IAAhBC,EACpB,OAAOH,EAKT,GAAIE,IAAaC,EAAa,CAC5B,IAEMK,EAAkB,oCAMpBN,GAAYA,GAAY,EACXF,EAAOS,UAAUP,EAAW,EAAGA,GAClBQ,MAAMF,IAGhC1B,EAASkB,EAAOW,OAAO,EAAGT,EAAW,GAAKF,EAAOW,OAAOT,GACpDvB,GAAWpB,KAAKgD,eAAe,GAAG,KAEtCzB,EAASkB,EAAOW,OAAO,EAAGT,EAAW,GAAKF,EAAOW,OAAOT,GACpDvB,GAAWpB,KAAKgD,eAAe,GAAG,IAGzBP,EAAOI,OAAO,GACDM,MAAMF,IAGhC1B,EAASkB,EAAOI,MAAM,GAAI,GACtBzB,GAAWpB,KAAKgD,eAAe,GAAG,KAEtCzB,EAASkB,EAAOI,MAAM,GAAI,GACtBzB,GAAWpB,KAAKgD,eAAe,GAAG,GAG5C,MACEzB,EAASkB,EAAOI,MAAM,EAAGF,GAAYF,EAAOI,MAAMD,GAC9CxB,GACFpB,KAAKL,UAAS,SAAC2C,GACbA,EAASC,iBAAiBI,EAC5B,IAIJ,OAAOpB,CACT,GAEA,CAAArB,IAAA,mBAAAC,MAMA,SACEsC,GAIA,IAKIlB,EARJoB,EAAgB/B,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAG6B,EAAO1C,OAC1B6C,EAAmBhC,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAG6B,EAAO1C,OAC7BqB,EAASR,UAAAb,OAAA,QAAAc,IAAAD,UAAA,IAAAA,UAAA,GAET,OAAK6B,SAAAA,EAAQ1C,QAAuB,OAAb4C,GAMnBA,IAAaC,EAWbrB,EAJmBkB,EAAOS,UAAUP,EAAUA,EAAW,GACzBQ,MAPV,qCAUbV,EAAOW,OAAO,EAAGT,GAAYF,EAAOW,OAAOT,EAAW,GAEtDF,EAAOW,OAAO,EAAGT,GAAYF,EAAOW,OAAOT,EAAW,IAGjEpB,EAASkB,EAAOI,MAAM,EAAGF,GAAYF,EAAOI,MAAMD,GAC9CxB,GACFpB,KAAKL,UAAS,SAAC2C,GACbA,EAASC,iBAAiBI,EAC5B,KAIGpB,GA7BEkB,CA8BX,GAEA,CAAAvC,IAAA,kBAAAC,MAMA,SAAgBkD,EAAyBC,GACvC,IAAMjC,EAAUrB,KAAKR,aACf+D,EAAYlC,EAAQkC,UACpBC,EAAeH,EAAShC,EAAQoC,WAAa,WAC7CC,EAAYJ,EAAavD,OAAS,GAAKwD,EAE7C,GAKED,EAAavD,QAAUyD,EAAazD,OAEpC,OAAO,EAGT,GAAI+B,OAAOC,UAAUwB,GAKnB,OAJIlC,EAAQW,OACVC,QAAQC,IAAI,2BAA4BwB,GAGtCA,GAIF1D,KAAK2D,kBAAmB,GACjB,IAEP3D,KAAK2D,kBAAmB,GACjB,GAIX,GAAyB,WAArBC,EAAOL,GAAwB,CACjC,IAAMG,EACJJ,EAAavD,OAAS,GAAKwD,EAAUlC,EAAQoC,WAAa,WAM5D,OAJIpC,EAAQW,OACVC,QAAQC,IAAI,2BAA4BwB,GAGtCA,GACF1D,KAAK2D,kBAAmB,GACjB,IAEP3D,KAAK2D,kBAAmB,GACjB,EAEX,CACF,GAEA,CAAAzD,IAAA,qBAAAC,MAGA,WACE,OAAO0D,QAAQ7D,KAAK2D,iBACtB,GAEA,CAAAzD,IAAA,gBAAAC,MAGA,WACE,MAAO,iBAAkB2D,QAAUC,UAAUC,cAC/C,GAEA,CAAA9D,IAAA,yBAAAC,MAGA,WACE,QAAS2D,OAAOG,YAClB,GAEA,CAAA/D,IAAA,YAAAC,MAoBA,SAAUuC,GACR,OAAKA,EAEEA,EACJwB,cACAC,OACAC,MAAM,aACNC,QAAO,SAAC3B,EAAK4B,GAAI,OAChBA,EAAKvE,OAAS2C,EAAM4B,EAAK,GAAGC,cAAgBD,EAAKzB,MAAM,GAAKH,CAAG,IAPlD,EASnB,GAEA,CAAAxC,IAAA,aAAAC,MAGA,SAAcqE,EAAUC,GACtB,OAAOC,EAAIC,MAAMC,KAAKC,KAAKL,EAAIzE,OAAS0E,KAAQK,KAAI,SAACC,EAAGC,GAAC,OACvDR,EAAI3B,MAAM4B,EAAOO,EAAGP,EAAOA,EAAOO,EAAE,GAExC,GAEA,CAAA9E,IAAA,cAAAC,MAGA,SAAYuC,GACV,OAAOA,EAAIlC,QAAQ,yBAA0B,OAC/C,GAEA,CAAAN,IAAA,eAAAC,MAGA,SAAa8E,EAAehE,GAC1B,IAAIiE,EAAWD,EACTE,EAAmBlE,EAAMmE,QAAQ,KAMvC,OAHGD,EAAmBF,IAA8B,GAArBE,GAAyBD,IAFjCjE,EAAMmE,QAAQ,KAGjBH,IAA8B,GAArBE,GAAyBD,IAE/CA,EAAW,EAAI,EAAIA,CAC5B,GAEA,CAAAhF,IAAA,gBAAAC,MAQA,SAAckF,GACZ,IACEC,QAAQC,UAAUC,OAAQ,GAAIH,EAChC,CAAE,MAAOI,GACP,OAAO,CACT,CACA,OAAO,CACT,I,EAAC,EAAAvF,IAAA,cAAAC,MAzED,SAAmBuF,EAAcpD,GAC/B,IACoEqD,EADpEC,E,8nBAAAC,CACuB/E,OAAOgF,oBAAoBJ,EAAQK,YAAU,IAApE,IAAAH,EAAAI,MAAAL,EAAAC,EAAAK,KAAAC,MAAsE,KAA3DC,EAAQR,EAAAxF,MAEF,gBAAbgG,GAA2C,gBAAbA,IAE9B7D,EAAS6D,GAAY7D,EAAS6D,GAAUC,KAAK9D,GAEjD,CAAC,OAAA+D,GAAAT,EAAAH,EAAAY,EAAA,SAAAT,EAAAP,GAAA,CACH,I,4FAAC,CAxfY,GAwfZxF,EAxfGP,EAAS,QA4iBC,WAAO,IAevB,U,m0BC/SA,QA5QsB,WAkBnB,O,EAXD,SAAAgH,EAAA/G,GAA8D,IAAAgH,EAAA,KAAhD5G,EAAQJ,EAARI,SAAUH,EAAUD,EAAVC,Y,4FAAUI,CAAA,KAAA0G,GAAAzG,EAAA,0BAAAA,EAAA,wBAAAA,EAAA,sBAyPlB,SAAC4F,GACf,OACEA,EAAEe,QACCf,EAAEgB,SACFhB,EAAEiB,UACF,CAAC,MAAO,WAAY,MAAO,UAAW,YAAa,YAAa,cAActG,SAC/EqF,EAAEkB,MAAQlB,EAAEvF,KAAOqG,EAAKK,aAAanB,aAAC,EAADA,EAAGoB,SAG9C,IA9PE7G,KAAKL,SAAWA,EAChBK,KAAKR,WAAaA,EAKlBF,EAAUW,YAAYqG,EAAkBtG,KAC1C,E,EAAC,EAAAE,IAAA,yBAAAC,MAED,SAAuBsF,GACrB,IAAMpE,EAAUrB,KAAKR,aAElB6B,EAAQyF,yCAA2C9G,KAAK+G,cAActB,KACvEA,EAAEuB,iBACFvB,EAAEwB,4BAGJ,IAAMC,EAAgBlH,KAAKmH,2BAA2B1B,GAEtDzF,KAAKL,UAAS,SAAC2C,GACb,IAII8E,EACAC,EALEC,EAAwBhF,EAASiF,iBAAiBL,GAClDM,EAAwBlF,EAASiF,iBAAiB,IAAD7G,OACjDwG,EAAa,MAKnB,GAAII,EACFF,EAAYE,EACZD,EAAaH,MACR,KAAIM,EAIT,OAHAJ,EAAYI,EACZH,EAAa,IAAH3G,OAAOwG,EAAa,IAGhC,CAEA,IAakEO,EAAAC,EAYAC,EAAAC,EAzB5DC,EAAmB,SAACC,GACxBA,EAAcC,MAAMC,WAClB3G,EAAQ4G,kCAAoC,UAC5CH,EAAcC,MAAMG,MACpB7G,EAAQ8G,oCAAsC,OAClD,EAEA,GAAIf,EACF,GAAGzC,MAAMyD,QAAQhB,IAIf,GAHAA,EAAUiB,SAAQ,SAAAP,GAAa,OAAID,EAAiBC,EAAc,IAG9DzG,EAAQiH,+BACV,GAAIjH,EAAQkH,+CACE,QAAZd,EAAAL,EAAU,UAAE,IAAAK,GAAe,QAAfC,EAAZD,EAAce,qBAAa,IAAAd,GAA3BA,EAAAe,KAAAhB,EAA8BhC,QACzB,GAAIpE,EAAQqH,uCAAwC,KAAAC,EAC7C,QAAZA,EAAAvB,EAAU,UAAE,IAAAuB,GAAZA,EAAcC,OAChB,MACEtG,EAASuG,oBAAoBxB,EAAY5B,QAI7CoC,EAAiBT,GAEb/F,EAAQiH,iCACNjH,EAAQkH,+CACD,QAATZ,EAAAP,SAAS,IAAAO,GAAe,QAAfC,EAATD,EAAWa,qBAAa,IAAAZ,GAAxBA,EAAAa,KAAAd,EAA2BlC,GAClBpE,EAAQqH,uCACjBtB,EAAUwB,QAEVtG,EAASuG,oBAAoBxB,EAAY5B,GAKnD,GACF,GAAC,CAAAvF,IAAA,uBAAAC,MAED,SAAqBsF,GACnB,IAAMpE,EAAUrB,KAAKR,aAElB6B,EAAQyF,yCAA2C9G,KAAK+G,cAActB,KACvEA,EAAEuB,iBACFvB,EAAEwB,4BAGJ,IAAMC,EAAgBlH,KAAKmH,2BAA2B1B,GAEtDzF,KAAKL,UAAS,SAAC2C,GACb,IAegEwG,EAAAC,EAMAC,EArB1D5B,EACJ9E,EAASiF,iBAAiBL,IAC1B5E,EAASiF,iBAAiB,IAAD7G,OAAKwG,EAAa,MAEvCW,EAAmB,SAACC,GACrBA,EAAcmB,iBACfnB,EAAcmB,gBAAgB,QAElC,EAEI7B,IACCzC,MAAMyD,QAAQhB,IACfA,EAAUiB,SAAQ,SAAAP,GAAa,OAAID,EAAiBC,EAAc,IAG9DzG,EAAQkH,iDACE,QAAZO,EAAA1B,EAAU,UAAE,IAAA0B,GAAa,QAAbC,EAAZD,EAAcI,mBAAW,IAAAH,GAAzBA,EAAAN,KAAAK,EAA4BrD,MAG9BoC,EAAiBT,GAEb/F,EAAQkH,iDACVnB,SAAsB,QAAb4B,EAAT5B,EAAW8B,mBAAW,IAAAF,GAAtBA,EAAAP,KAAArB,EAAyB3B,KAIjC,GACF,GAEA,CAAAvF,IAAA,6BAAAC,MAIA,SAA2BsF,GAAkB,IAAA0D,EACvC5H,EAAS,GACP6H,EAAQ3D,EAAEkB,MAAQlB,EAAEvF,KAAOF,KAAK4G,aAAanB,aAAC,EAADA,EAAGoB,SAgBtD,OALEtF,EARA6H,SAAAA,EAAOhJ,SAAS,WAChBgJ,SAAAA,EAAOhJ,SAAS,UAChBgJ,SAAAA,EAAOhJ,SAAS,UAChBgJ,SAAAA,EAAOhJ,SAAS,cAChBgJ,SAAAA,EAAOhJ,SAAS,YAChBgJ,SAAAA,EAAOhJ,SAAS,QAChBgJ,SAAAA,EAAOhJ,SAAS,QAEPqF,EAAEkB,MAAQ,GAEVlB,EAAEvF,KAAOF,KAAK4G,aAAanB,aAAC,EAADA,EAAGoB,UAAY,IAGvC9G,OAAS,EAAU,QAAToJ,EAAG5H,SAAM,IAAA4H,OAAA,EAANA,EAAQjF,cAAgB3C,CACrD,GAEA,CAAArB,IAAA,eAAAC,MAGA,SAAa0G,GACX,MAAO,CACL,EAAG,YACH,EAAG,MACH,GAAI,QACJ,GAAI,QACJ,GAAI,OACJ,GAAI,MACJ,GAAI,QACJ,GAAI,WACJ,GAAI,MACJ,GAAI,QACJ,GAAI,SACJ,GAAI,WACJ,GAAI,MACJ,GAAI,OACJ,GAAI,YACJ,GAAI,UACJ,GAAI,aACJ,GAAI,YACJ,GAAI,SACJ,GAAI,SACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,IACJ,GAAI,OACJ,GAAI,UACJ,GAAI,UACJ,GAAI,UACJ,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,iBACL,IAAK,YACL,IAAK,iBACL,IAAK,gBACL,IAAK,eACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,MACL,IAAK,MACL,IAAK,MACL,IAAK,UACL,IAAK,aACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,KACL,IAAK,IACL,IAAK,KACLA,IAAY,EAChB,I,4FAAC,CA9PmB,G,m0BC2JtB,QAvJkB,WAYf,O,EALD,SAAAwC,EAAA9J,GAAwD,IAA1C+J,EAAS/J,EAAT+J,UAAWjI,EAAO9B,EAAP8B,S,4FAAOzB,CAAA,KAAAyJ,GAAAxJ,EAAA,yBAAAA,EAAA,uBAAAA,EAAA,mCAAAA,EAAA,iBAHpB,GAACA,EAAA,wBAIXG,KAAKsJ,UAAYA,EACjBtJ,KAAKqB,QAAUA,EACf/B,EAAUW,YAAYoJ,EAAcrJ,MACpCA,KAAKuJ,SAAWvJ,KAAKsJ,UAAU9J,aAAagK,0BAA4B,CAC1E,E,EAAC,EAAAtJ,IAAA,UAAAC,MAED,WACMH,KAAKyJ,sBACPzJ,KAAKyJ,oBAAoBC,SACzB1J,KAAK2J,UAAY,EAErB,GAAC,CAAAzJ,IAAA,OAAAC,MAED,SAAIyJ,GAI6B,IAAArD,EAAA,KAH/BsD,EAAcD,EAAdC,eACAC,EAAaF,EAAbE,cACAC,EAAQH,EAARG,SAEA,GAAKF,GAAmBA,EAAe9J,OAAvC,CAIA,IAAMiK,EAAqBhK,KAAKsJ,UAAUW,WACxCJ,EAAezF,MAAM,KACrBpE,KAAKuJ,UAGPvJ,KAAKkK,WAAW,CACdF,mBAAAA,EACAF,cAAAA,EACAH,UAAW3J,KAAK2J,UAChBQ,QAASH,EAAmBjK,OAC5BqK,eAAgB,SAACC,EAA2B5E,GAC1CsE,EAASM,EAAmB5E,GAC5Bc,EAAK+D,SACP,GAfF,CAiBF,GAAC,CAAApK,IAAA,aAAAC,MAED,SAAUoK,GAMmB,IAAAC,EAAAC,EAAA,KAL3BT,EAAkBO,EAAlBP,mBACAF,EAAaS,EAAbT,cACAH,EAASY,EAATZ,UACAQ,EAAOI,EAAPJ,QACAC,EAAcG,EAAdH,eAGwB,QAAxBI,EAAAxK,KAAKyJ,2BAAmB,IAAAe,GAAxBA,EAA0Bd,SAG1B1J,KAAKyJ,oBAAsBiB,SAASC,cAAc,OAClD3K,KAAKyJ,oBAAoBmB,UAAY,mBAGrC,IAAMC,EAAyBH,SAASC,cAAc,MACtDE,EAAuBD,UAAY,wBAGnCZ,EAAmBL,GAAWtB,SAAQ,SAACyC,GAAsB,IAAAC,EACrDC,EAAyBN,SAASC,cAAc,MAChDM,EAAgB,WACpB,IAAMC,EAAa,IAAKT,EAAKpJ,QAAQ8J,eAAiBC,WAAaC,YAAY,SAI/E,OAHAvK,OAAOwK,eAAeJ,EAAY,SAAU,CAC1C/K,MAAO6K,IAEFE,CACT,EAEAF,EAAuBJ,UAAY,6BACnCI,EAAuBO,WAAgC,QAApBR,EAAAN,EAAKpJ,QAAQV,eAAO,IAAAoK,OAAA,EAApBA,EAAuBD,KAAsBA,EAE7EL,EAAKpJ,QAAQ8J,eACdH,EAAuBQ,aAAe,SAAC/F,GAAM,OAC3C2E,EAAeU,EAAmBrF,GAAKwF,IAAgB,EAEzDD,EAAuBS,QAAU,eAAChG,EAAC7E,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAGqK,IAAe,OACnDb,EAAeU,EAAmBrF,EAAE,EAIxCoF,EAAuBa,YAAYV,EACrC,IAGA,IAAMW,EAAyBhC,EAAY,EACrCiC,EAAiBlB,SAASC,cAAc,OAC9CiB,EAAeC,UAAUC,IAAI,yBAC7BH,GACEC,EAAeC,UAAUC,IAAI,+BAE/B,IAAMC,EAA4B,WAC3BJ,GACLlB,EAAKP,WAAW,CACdF,mBAAAA,EACAF,cAAAA,EACAH,UAAWA,EAAY,EACvBQ,QAAAA,EACAC,eAAAA,GAEJ,EAEGpK,KAAKqB,QAAQ8J,eACdS,EAAeJ,aAAeO,EAE9BH,EAAeH,QAAUM,EAG3B/L,KAAKyJ,oBAAoBiC,YAAYE,GAGrC5L,KAAKyJ,oBAAoBiC,YAAYb,GAGrC,IAAMmB,EAAyBrC,EAAYQ,EAAU,EAC/C8B,EAAiBvB,SAASC,cAAc,OAC9CsB,EAAeJ,UAAUC,IAAI,yBAC7BE,GACEC,EAAeJ,UAAUC,IAAI,+BAE/B,IAAMI,EAA4B,WAC3BF,GACLvB,EAAKP,WAAW,CACdF,mBAAAA,EACAF,cAAAA,EACAH,UAAWA,EAAY,EACvBQ,QAAAA,EACAC,eAAAA,GAEJ,EAEGpK,KAAKqB,QAAQ8J,eACdc,EAAeT,aAAeU,EAE9BD,EAAeR,QAAUS,EAG3BlM,KAAKyJ,oBAAoBiC,YAAYO,GAGrCnC,EAAcqC,QAAQnM,KAAKyJ,oBAC7B,I,4FAAC,CApJe,G,+vDC8hElB,QAlhEoB,WA0NjB,O,EA5LD,SAAA2C,EACEC,EACAC,GACA,IAAA/F,EAAA,KACA,G,4FADA3G,CAAA,KAAAwM,GAAAvM,EAAA,qBAAAA,EAAA,uBAAAA,EAAA,yBAAAA,EAAA,6BAAAA,EAAA,gCAAAA,EAAA,2BAAAA,EAAA,qCAAAA,EAAA,gCAAAA,EAAA,8BAAAA,EAAA,mCAAAA,EAAA,oCAAAA,EAAA,qCAAAA,EAAA,uCAAAA,EAAA,gCAAAA,EAAA,uBAAAA,EAAA,iCAAAA,EAAA,sCAAAA,EAAA,2BAAAA,EAAA,2BAAAA,EAAA,2BAAAA,EAAA,4BAAAA,EAAA,+BAAAA,EAAA,mBAVY,WAASA,EAAA,0BAC6C,MAoMpEA,EAAA,qBAGe,SACbwM,EACAC,GAMA,IAAIC,EACAC,EACAnL,EAMJ,GAAiC,iBAAtBgL,EACTE,EAAmBF,EAAkBjI,MAAM,KAAKtB,KAAK,IACrD0J,EAAc9B,SAAS+B,cAAc,IAAD/L,OAC9B6L,IAENlL,EAAUiL,OAML,GAAID,aAA6BK,eAAgB,CAItD,IAAKL,EAAkBzB,UAErB,MADA3I,QAAQ0K,KAAK,0DACP,IAAIC,MAAM,4BAGlBL,EAAmBF,EAAkBzB,UAAUxG,MAAM,KAAK,GAC1DoI,EAAcH,EACdhL,EAAUiL,CAKZ,MACEC,EAAmB,kBACnBC,EAAc9B,SAAS+B,cAAc,IAAD/L,OAC9B6L,IAENlL,EAAUgL,EAGZ,MAAO,CACLE,iBAAAA,EACAC,YAAAA,EACAnL,QAAAA,EAEJ,IAEAxB,EAAA,mBAGa,kBAAuB0G,EAAKlF,OAAO,IAAAxB,EAAA,yBAC7B,kBAAqB0G,EAAK/D,aAAa,IAAA3C,EAAA,4BACpC,kBAAqB0G,EAAKsG,gBAAgB,IA6wChEhN,EAAA,uBAGiB,SAACiN,EAAcC,GACzBxG,EAAKyG,QAAQF,KAAOvG,EAAKyG,QAAQF,GAAQ,CAAC,GAE/CC,EAAaxG,EAAKyG,QAAQF,GAC5B,IA2HAjN,EAAA,+BAGyB,WAA8B,QAAAoN,EAAArM,UAAAb,OAA1BmN,EAAc,IAAAvI,MAAAsI,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAdD,EAAcC,GAAAvM,UAAAuM,GAKzC,MAJwB,CAAC5G,EAAKgG,kBAAgB7L,OAAKwM,GAAgBE,QACjE,SAACC,GAAQ,QAAOA,CAAQ,IAGHvK,KAAK,IAC9B,IAppDwB,oBAAXgB,OAAX,CAEA,IAAAwJ,EAIItN,KAAKuN,aAAalB,EAAmBC,GAHvCC,EAAgBe,EAAhBf,iBACAC,EAAWc,EAAXd,YAAWgB,EAAAF,EACXjM,QAAAA,OAAO,IAAAmM,EAAG,CAAC,EAACA,EAMdxN,KAAKsJ,UAAY,IAAIhK,EAAU,CAC7BE,WAAYQ,KAAKR,WACjBC,iBAAkBO,KAAKP,iBACvBC,oBAAqBM,KAAKN,oBAC1BC,SAAUK,KAAKL,WAMjBK,KAAKwC,cAAgB,KAKrBxC,KAAK6M,iBAAmB,KAKxB7M,KAAKwM,YAAcA,EAuDnBxM,KAAKqB,Q,+VAAOoM,CAAA,CACVC,WAAY,UACZC,MAAO,mBACPlK,UAAW,UACXmK,yBAAyB,EACzBC,wBAAwB,EACxBC,kBAAmB,CAAC,GACjBzM,GAMLrB,KAAK+N,sBAAwB,GAK7BzO,EAAUW,YAAYmM,EAAgBpM,MAgBtC,IAAAgO,EAAyChO,KAAKqB,QAAtCoC,UAAAA,OAAS,IAAAuK,EAAGhO,KAAKiO,YAAWD,EAqDpC,GApDAhO,KAAKiB,MAAQ,CAAC,EACdjB,KAAKiB,MAAMwC,GAAa,GAKxBzD,KAAKuM,iBAAmBA,EAKxBvM,KAAKkO,eAAiB,CAAC,EAMjBpK,OAA6C,0BAChDA,OAA6C,wBAAI,CAAC,GAErD9D,KAAKmO,oBAAsBnO,KAAKsJ,UAAU8E,UAAUpO,KAAKuM,kBACxDzI,OAA6C,wBAAE9D,KAAKmO,qBAAuBnO,KAK5EA,KAAKqO,qBAAwBvK,OAA6C,wBAC1E9D,KAAKsO,sBAAwBxN,OAAOyN,KAAMzK,OAA6C,yBACvF9D,KAAKwO,wBACHxO,KAAKsO,sBAAsB,KAAOtO,KAAKmO,oBAKzCnO,KAAKyO,iBAAmB,IAAInI,EAAiB,CAC3C3G,SAAUK,KAAKL,SACfH,WAAYQ,KAAKR,aAMnBQ,KAAK0O,aAAe1O,KAAKqB,QAAQwM,uBAC7B,IAAIxE,EAAa,CACfC,UAAWtJ,KAAKsJ,UAChBjI,QAASrB,KAAKqB,UAEhB,MAKArB,KAAKwM,YAGP,MADAvK,QAAQ0K,KAAK,KAADjM,OAAM6L,EAAgB,gCAC5B,IAAIK,MAAM,sBAHI5M,KAAK2O,SAS3B3O,KAAKgN,QAAU,CAAC,EAChBhN,KAAK4O,aAvLoC,CAwL3C,E,EAAC,EAAA1O,IAAA,mBAAAC,MA2ED,SAAiBwC,GAAuD,IAA9BkM,EAAWjO,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAG+B,EACtD3C,KAAKwC,cAAgBG,EACrB3C,KAAK6M,iBAAmBgC,CAC1B,GAEA,CAAA3O,IAAA,qBAAAC,MAIA,SACEc,GAC0E,IAAAwJ,EAAA,KAC1EqE,EAGI9O,KAAKqB,QAFW0N,EAAmBD,EAArCE,iBACAC,EAAkCH,EAAlCG,mCAGF,IAAKF,GAAsD,WAA/BnL,EAAOmL,GACjC,MAAO,CAAC,EAGV,IAAMC,EAAmBlO,OAAOyN,KAAKQ,GAAqB3B,QACxD,SAAC8B,GACC,IAAMC,EACJlO,EAAMiC,UAAU,EAAGuH,EAAK/K,uBAAyB,IAAMuB,EACnDmO,EAAS,IAAIC,OAAO,GAAD3O,OACpB+J,EAAKnB,UAAUgG,YAAYJ,GAAgB,KAC9CD,EAAqC,IAAM,MAG7C,QADavK,EAAOyK,EAAYI,SAASH,IACxBrP,MACnB,IAGF,GAAIiP,EAAiBjP,OAAS,EAAG,CAC/B,IAAMyP,EAAeR,EAAiBS,MACpC,SAACC,EAAGC,GAAC,OAAKA,EAAE5P,OAAS2P,EAAE3P,MAAM,IAC7B,GACF,MAAO,CACLyP,aAAAA,EACA3F,eAAgBkF,EAAoBS,GAExC,CAAO,GAAIR,EAAiBjP,OAAQ,CAClC,IAAMyP,EAAeR,EAAiB,GACtC,MAAO,CACLQ,aAAAA,EACA3F,eAAgBkF,EAAoBS,GAExC,CACE,MAAO,CAAC,CAEZ,GAEA,CAAAtP,IAAA,oBAAAC,MAKA,SACEqP,EACA3F,EACAC,GACM,IAAA8F,EAAA,KACF5P,KAAK0O,cACP1O,KAAK0O,aAAamB,KAAK,CACrBhG,eAAAA,EACAC,cAAAA,EACAC,SAAU,SAACM,EAA2B5E,GACpC,IAAAqK,EAIIF,EAAKvO,QAHP4N,EAAkCa,EAAlCb,mCACAc,EAA6BD,EAA7BC,8BACAC,EAA8BF,EAA9BE,+BAGEC,EAAe5F,EAEf0F,IAIFE,EAAe5F,EAAkB6F,UAAU,QAMC,mBAAnCN,EAAKvO,QAAQ8O,mBACtBP,EAAKvO,QAAQ8O,kBAAkBP,GAGjC,IAAMpM,EAAeoM,EAAKQ,SAASR,EAAKvO,QAAQoC,WAAW,GACrD4M,EAAuBT,EAAKlQ,uBAAyB,EACrDyP,EACJ3L,EAAaN,UAAU,EAAGmN,GAAwB,IAClD7M,EAEI4L,EAAS,IAAIC,OAAO,GAAD3O,OACpBkP,EAAKtG,UAAUgG,YAAYE,GAAa,KAC3CP,EAAqC,IAAM,MAEvCqB,EAAiBnB,EAAY3O,QACjC4O,EACAa,GAEIM,EAAW/M,EAAahD,QAAQ2O,EAAamB,GAE7CE,EAAoBF,EAAevQ,OAASoP,EAAYpP,OAC1D0Q,GACDJ,GAAwB7M,EAAazD,QAAUyQ,EAE9CC,EAAmB,IAAGA,EAAmB,GAE7Cb,EAAKc,SAASH,EAAUX,EAAKvO,QAAQoC,WAAW,GAChDmM,EAAKrN,iBAAiBkO,GAOlBT,GAAqE,mBAA5BJ,EAAKvO,QAAQsP,YACxDf,EAAKvO,QAAQsP,WAAWtG,EAAmB5E,GAER,mBAA1BmK,EAAKvO,QAAQuP,UACtBhB,EAAKvO,QAAQuP,SACXhB,EAAKQ,SAASR,EAAKvO,QAAQoC,WAAW,GACtCgC,GAMoC,mBAA7BmK,EAAKvO,QAAQwP,aACtBjB,EAAKvO,QAAQwP,YAAYjB,EAAKkB,eAAgBrL,EAClD,GAGN,GAEA,CAAAvF,IAAA,sBAAAC,MAIA,SAAoBL,EAAgB2F,GAClC,IAAAsL,EAAgD/Q,KAAKqB,QAAO2P,EAAAD,EAApDtN,UAAAA,OAAS,IAAAuN,EAAGhR,KAAKiO,YAAW+C,EAAEhP,EAAK+O,EAAL/O,MAItC,GAAe,SAAXlC,EAAJ,CAKKE,KAAKiB,MAAMwC,KAAYzD,KAAKiB,MAAMwC,GAAa,IAKN,mBAAnCzD,KAAKqB,QAAQ8O,mBACtBnQ,KAAKqB,QAAQ8O,kBAAkBnQ,MAMjC,IAAMsD,EAAetD,KAAKsJ,UAAU2H,gBAClCnR,EACAE,KAAKiB,MAAMwC,GACXzD,KAAKwC,cACLxC,KAAK6M,kBAMP,GAAI7M,KAAKsJ,UAAU4H,iBAAiBpR,IAAWE,KAAKmR,oBAEhDnR,KAAKiB,MAAMwC,IACXzD,KAAKiB,MAAMwC,KAAeH,GACH,IAAvBtD,KAAKwC,eACLxC,KAAK6M,mBAAqBvJ,EAAavD,OAQvC,OALAC,KAAK0Q,SAAS,GAAI1Q,KAAKqB,QAAQoC,WAAW,GAC1CzD,KAAKuC,iBAAiB,GACtBvC,KAAKmR,mBAAmBhR,MAAQ,GAChCH,KAAKmR,mBAAmBC,kBAAkB,EAAG,QAC7CpR,KAAK6I,oBAAoB/I,EAAQ2F,GAWrC,GAHuC,mBAA5BzF,KAAKqB,QAAQsP,YACtB3Q,KAAKqB,QAAQsP,WAAW7Q,EAAQ2F,GAIhCzF,KAAKiB,MAAMwC,KAAeH,KAGxBtD,KAAKqB,QAAQgQ,cAEZrR,KAAKqB,QAAQgQ,cAAgBrR,KAAKsR,oBAAoBhO,IACzD,CAIA,GACEtD,KAAKqB,QAAQkC,WACbvD,KAAKsJ,UAAUiI,gBAAgBvR,KAAKiB,MAAOqC,GAE3C,OAMF,IAAMkO,EAAgBxR,KAAKsJ,UAAU2H,gBACnCnR,EACAE,KAAKiB,MAAMwC,GACXzD,KAAKwC,cACLxC,KAAK6M,kBACL,GAqCF,GAlCA7M,KAAK0Q,SAASc,EAAexR,KAAKqB,QAAQoC,WAAW,GAEjDzB,GAAOC,QAAQC,IAAI,iBAAkBlC,KAAK8Q,gBAE1C9Q,KAAKqB,QAAQW,OACfC,QAAQC,IACN,aACAlC,KAAKP,mBACLO,KAAKN,sBAAqB,IAAAgB,OACtBV,KAAKuM,iBAAgB,KACzB9G,aAAC,EAADA,EAAGgM,MAOHzR,KAAKqB,QAAQqQ,oBAAoB1R,KAAK0R,qBAKL,mBAA1B1R,KAAKqB,QAAQuP,UACtB5Q,KAAKqB,QAAQuP,SAAS5Q,KAAKoQ,SAASpQ,KAAKqB,QAAQoC,WAAW,GAAOgC,GAK7B,mBAA7BzF,KAAKqB,QAAQwP,aACtB7Q,KAAKqB,QAAQwP,YAAY7Q,KAAK8Q,eAAgBrL,GAK5CA,SAAAA,EAAGkM,QAAU3R,KAAKqB,QAAQwM,uBAAwB,CACpD,IASO+D,EATPC,EACE7R,KAAK8R,mBAAmBxO,GADlBkM,EAAYqC,EAAZrC,aAAc3F,EAAcgI,EAAdhI,eAGlB2F,GAAgB3F,EAClB7J,KAAK+R,kBACHvC,EACA3F,EACA7J,KAAKwM,aAGU,QAAjBoF,EAAA5R,KAAK0O,oBAAY,IAAAkD,GAAjBA,EAAmBtH,SAEvB,CACF,CAMGtK,KAAK6M,kBAAoB7M,KAAKwC,gBAAkBxC,KAAK6M,mBACtD7M,KAAKuC,iBAAiBvC,KAAK6M,iBAAkB7M,KAAK6M,kBAE/C7M,KAAKmR,oBACNnR,KAAKmR,mBAAmBC,kBAAkBpR,KAAK6M,iBAAkB7M,KAAK6M,kBAGrE7M,KAAKqB,QAAQW,OACdC,QAAQC,IAAI,yBAA0BlC,KAAKwC,gBAI3CR,GACFC,QAAQC,IAAI,eAAgBpC,EAnJD,CAqJ/B,GAEA,CAAAI,IAAA,eAAAC,MAGA,WACE,OAAOH,KAAKgS,WACd,GAEA,CAAA9R,IAAA,eAAAC,MAGA,SAAaA,GACPH,KAAKqB,QAAQqQ,mBACf1R,KAAKL,UAAS,SAAC2C,GACbA,EAAS0P,YAAc7R,CACzB,IAEAH,KAAKgS,YAAc7R,CAEvB,GAKA,CAAAD,IAAA,wBAAAC,MACA,SAAsBL,EAAgB2F,GAA+B,IAAAwM,EAAA,KAC/DxM,IAIEzF,KAAKqB,QAAQuM,yBAAyBnI,EAAEuB,iBACxChH,KAAKqB,QAAQ6Q,0BAA0BzM,EAAE0M,kBAK7C1M,EAAEkM,OAAO9F,UAAUC,IAAI9L,KAAKoS,oBAG1BpS,KAAKqS,wBAAwBC,aAAatS,KAAKqS,wBAC/CrS,KAAKuS,aAAaD,aAAatS,KAAKuS,aAKxCvS,KAAKwS,cAAa,GAKbxS,KAAKqB,QAAQoR,oBAChBzS,KAAKuS,YAAczO,OAAO4O,YAAW,YAEhCT,EAAKU,kBAED7S,EAAOM,SAAS,OAASN,EAAOM,SAAS,MAC/B,aAAXN,GACW,gBAAXA,GACW,WAAXA,GACW,YAAXA,GACW,UAAXA,IACO,iBAAXA,GACW,gBAAXA,GACW,cAAXA,GACW,gBAAXA,KAEImS,EAAK5Q,QAAQW,OAAOC,QAAQC,IAAI,eAAgBpC,GAEpDmS,EAAKW,iBAAiB9S,IAExBwS,aAAaL,EAAKM,YACpB,GAAG,KAEP,GAEA,CAAArS,IAAA,sBAAAC,MAGA,SAAoBL,EAAiB2F,GAAgC,IAAAoN,EAAA,KAC/DpN,IAIEzF,KAAKqB,QAAQyR,uBAAyBrN,EAAEuB,gBAC1CvB,EAAEuB,iBACAhH,KAAKqB,QAAQ0R,wBAA0BtN,EAAE0M,iBAC3C1M,EAAE0M,oBAIF1M,EAAEkM,SAAW3R,KAAKwM,aACjB/G,EAAEkM,QAAU3R,KAAKwM,YAAYwG,SAASvN,EAAEkM,SACxC3R,KAAK0O,cACJ1O,KAAK0O,aAAajF,sBACjBhE,EAAEkM,SAAW3R,KAAK0O,aAAajF,qBAC7BhE,EAAEkM,QACD3R,KAAK0O,aAAajF,oBAAoBuJ,SAASvN,EAAEkM,WAKtC3R,KAAK0O,cACtB1O,KAAK0O,aAAapE,WAOtBtK,KAAKiT,gBAAe,SAACnL,GACnBA,EAAc+D,UAAUnC,OAAOmJ,EAAKT,kBACtC,IAEApS,KAAKwS,cAAa,GACdxS,KAAKqS,wBAAwBC,aAAatS,KAAKqS,wBAK/CvS,GAAgD,mBAA/BE,KAAKqB,QAAQ6R,eAChClT,KAAKqB,QAAQ6R,cAAcpT,EAAQ2F,EACvC,GAEA,CAAAvF,IAAA,mCAAAC,MAGA,SAAiCsF,GAI3BzF,KAAKqB,QAAQuM,yBAAyBnI,EAAEuB,gBAC9C,GAKA,CAAA9G,IAAA,mBAAAC,MACA,SAAiBL,GAAsB,IAAAqT,EAAA,KACjCnT,KAAKqS,wBAAwBC,aAAatS,KAAKqS,wBAKnDrS,KAAKqS,uBAAyBvO,OAAO4O,YAAW,WAC1CS,EAAKR,gBACPQ,EAAKtK,oBAAoB/I,GACzBqT,EAAKP,iBAAiB9S,IAEtBwS,aAAaa,EAAKd,uBAEtB,GAAG,IACL,GAEA,CAAAnS,IAAA,qBAAAC,MAGA,WAA2B,IAAAiT,EAAA,KACzBpT,KAAKL,UAAS,SAAC2C,GACbA,EAAS+Q,aAAaD,EAAKnS,OAC3BqB,EAASC,iBAAiB6Q,EAAK5Q,cAAe4Q,EAAKvG,iBACrD,GACF,GAEA,CAAA3M,IAAA,aAAAC,MAIA,WAEQ,IADNsD,EAAiB7C,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAGZ,KAAKqB,QAAQoC,WAAazD,KAAKiO,YAEnDjO,KAAKiB,MAAMwC,GAAa,GAKxBzD,KAAKuC,iBAAiB,GAKlBvC,KAAKqB,QAAQqQ,oBAAoB1R,KAAK0R,oBAC5C,GAEA,CAAAxR,IAAA,WAAAC,MAIA,WAGU,IAFRsD,EAAiB7C,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAGZ,KAAKqB,QAAQoC,WAAazD,KAAKiO,YACnDqF,EAAQ1S,UAAAb,OAAA,QAAAc,IAAAD,UAAA,IAAAA,UAAA,GAOR,OAFIZ,KAAKqB,QAAQqQ,qBAAuB4B,GAAUtT,KAAK0R,qBAEnD1R,KAAKqB,QAAQkS,IAMR,IAJwBvT,KAAKiB,MAAMwC,GACvCjD,QAAQ,IAAU,IAClBA,QAAQ,IAAU,IAEsB,IAEpCR,KAAKiB,MAAMwC,EAEtB,GAEA,CAAAvD,IAAA,eAAAC,MAGA,WAA8B,IAAAqT,EAAA,KACtBjS,EAAS,CAAC,EAOhB,OANmBT,OAAOyN,KAAKvO,KAAKiB,OAEzBoH,SAAQ,SAAC5E,GAClBlC,EAAOkC,GAAa+P,EAAKpD,SAAS3M,GAAW,EAC/C,IAEOlC,CACT,GAEA,CAAArB,IAAA,WAAAC,MAKA,SACEc,GAGM,IAFNwC,EAAiB7C,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAGZ,KAAKqB,QAAQoC,WAAazD,KAAKiO,YACnDqF,EAAkB1S,UAAAb,OAAA,EAAAa,UAAA,QAAAC,EAElBb,KAAKiB,MAAMwC,GAAaxC,GAKnBqS,GAAYtT,KAAKqB,QAAQqQ,oBAAoB1R,KAAK0R,oBACzD,GAEA,CAAAxR,IAAA,eAAAC,MAIA,SAAakD,GACXrD,KAAKiB,MAAQoC,CACf,GAEA,CAAAnD,IAAA,aAAAC,MAIA,WAA+B,IAApBkB,EAAOT,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EACd6S,EAAiBzT,KAAKyT,eAAepS,GAC3CrB,KAAKqB,QAAUP,OAAOC,OAAOf,KAAKqB,QAASA,GAEvCoS,EAAe1T,SACbC,KAAKqB,QAAQW,OACfC,QAAQC,IAAI,iBAAkBuR,GAMhCzT,KAAK0T,aAAaD,GAKlBzT,KAAK2O,SAET,GAEA,CAAAzO,IAAA,iBAAAC,MAIA,SAAewT,GAAgD,IAAAC,EAAA,KAC7D,OAAO9S,OAAOyN,KAAKoF,GAAYvG,QAC7B,SAACyG,GAAU,OACTC,KAAKC,UAAUJ,EAAWE,MAC1BC,KAAKC,UAAUH,EAAKvS,QAAQwS,GAAY,GAE9C,GAEA,CAAA3T,IAAA,eAAAC,MAIA,WAAkD,IAArCsT,EAAwB7S,UAAAb,OAAA,QAAAc,IAAAD,UAAA,GAAAA,UAAA,GAAG,GAIlC6S,EAAerT,SAAS,eAItBJ,KAAK0O,cACP1O,KAAK0O,aAAapE,WAQpBmJ,EAAerT,SAAS,6BACxBqT,EAAerT,SAAS,sBAKpBJ,KAAK0O,eACP1O,KAAK0O,aAAapE,UAClBtK,KAAK0O,aAAe,IAAIrF,EAAa,CACnCC,UAAWtJ,KAAKsJ,UAChBjI,QAASrB,KAAKqB,UAItB,GAEA,CAAAnB,IAAA,YAAAC,MAIA,WACMH,KAAKgU,iBACPhU,KAAKgU,gBAAgBtK,SAGvB1J,KAAKwM,YAAY5B,UAAY5K,KAAKuM,iBAClCvM,KAAKwM,YAAYyH,aAAa,kBAAmBjU,KAAKmO,qBACtDnO,KAAKkO,eAAiB,CAAC,CACzB,GAMA,CAAAhO,IAAA,WAAAC,MACA,SAAS+T,GACP,IAAMpQ,OAA6C,wBAIjD,MAHA7B,QAAQ0K,KAAK,sEAGP,IAAIC,MAAM,uBAGlB,OAAO9L,OAAOyN,KAAMzK,OAA6C,yBAAGuE,SAAQ,SAACnI,GAC3EgU,EAAUpQ,OAA6C,wBAAE5D,GAAMA,EACjE,GACF,GAEA,CAAAA,IAAA,iBAAAC,MAKA,SAAegU,EAAiBvJ,GAAyB,IAAAwJ,EAAA,KAClDxJ,GAAcuJ,IAEnBA,EAAQ/P,MAAM,KAAKiE,SAAQ,SAACvI,GAC1B8K,EAAUxG,MAAM,KAAKiE,SAAQ,SAACgM,GACvBD,EAAK/S,QAAQiT,cAAaF,EAAK/S,QAAQiT,YAAc,IAE1D,IAAIC,GAAiB,EAKrBH,EAAK/S,QAAQiT,YAAYxP,KAAI,SAACwP,GAC5B,GAAIA,SAAAA,EAAW,MAAQlQ,MAAM,KAAKhE,SAASiU,GAAgB,CACzDE,GAAiB,EAEjB,IAAMC,EAAmBF,EAAYH,QAAQ/P,MAAM,KAC9CoQ,EAAiBpU,SAASN,KAC7ByU,GAAiB,EACjBC,EAAiBC,KAAK3U,GACtBwU,EAAYH,QAAUK,EAAiB1R,KAAK,KAEhD,CACA,OAAOwR,CACT,IAKKC,GACHH,EAAK/S,QAAQiT,YAAYG,KAAK,CAC5BC,MAAOL,EACPF,QAASA,GAGf,GACF,IAEAnU,KAAK2O,SACP,GAEA,CAAAzO,IAAA,oBAAAC,MAKA,SAAkBgU,EAAiBvJ,GAAyB,IAAA+J,EAAA,KAI1D,IAAKR,IAAYvJ,EAGf,OAFA5K,KAAKqB,QAAQiT,YAAc,QAC3BtU,KAAK2O,SAQLwF,GACAxP,MAAMyD,QAAQpI,KAAKqB,QAAQiT,cAC3BtU,KAAKqB,QAAQiT,YAAYvU,SAELoU,EAAQ/P,MAAM,KACtBiE,SAAQ,SAACvI,GAAW,IAAA8U,EAClB,QAAZA,EAAAD,EAAKtT,eAAO,IAAAuT,GAAa,QAAbA,EAAZA,EAAcN,mBAAW,IAAAM,GAAzBA,EAA2B9P,KAAI,SAACwP,EAAarP,GAK3C,GACGqP,GACC1J,GACAA,EAAUxK,SAASkU,EAAW,SAC/B1J,EACD,KAAAiK,EAUOC,EATDC,EAAiC,QAAdF,EAAGP,SAAW,IAAAO,OAAA,EAAXA,EAAaV,QACtC/P,MAAM,KACNgJ,QAAO,SAAC4H,GAAI,OAAKA,IAASlV,CAAM,IAK/BwU,SAAeS,GAAAA,EAAqBhV,OACtCuU,EAAYH,QAAUY,EAAoBjS,KAAK,MAEvB,QAAxBgS,EAAAH,EAAKtT,QAAQiT,mBAAW,IAAAQ,GAAxBA,EAA0BG,OAAOhQ,EAAO,GACxCqP,EAAc,KAElB,CAEA,OAAOA,CACT,GACF,IAEAtU,KAAK2O,SAET,GAEA,CAAAzO,IAAA,mBAAAC,MAIA,SACEL,GAEA,IAAIyB,EAEE2T,EAAYlV,KAAKkO,eAAepO,GAStC,OARIoV,IAEA3T,EADE2T,EAAUnV,OAAS,EACZmV,EAEAA,EAAU,IAIhB3T,CACT,GAEA,CAAArB,IAAA,sBAAAC,MAIA,SAAoBgV,GAClB,IACI9D,EADE+D,EAAkBpV,KAAKqB,QAAQgQ,aAarC,IANEA,EADE+D,aAA2B/F,OACd+F,EAGbA,EAAgBpV,KAAKqB,QAAQoC,WAAazD,KAAKiO,eAG/BkH,EAAU,CAC5B,IAAME,EAAgBhE,EAAaiE,KAAKH,GAUxC,OARInV,KAAKqB,QAAQW,OACfC,QAAQC,IAAI,kBAADxB,OACS2Q,EAAY,QAAA3Q,OAC5B2U,EAAgB,SAAW,kBAK1BA,CACT,CAIE,OAAO,CAEX,GAEA,CAAAnV,IAAA,oBAAAC,MAGA,WAIE,GAAIH,KAAKwO,0BAA4BxO,KAAKqO,qBAAsB,CAC1DrO,KAAKqB,QAAQW,OACfC,QAAQC,IAAI,2BAADxB,OAA4BV,KAAKuM,iBAAgB,MAG9D,IAAAgJ,EAA4DvV,KAAKqB,QAAzDyF,wCAAAA,OAAuC,IAAAyO,GAAQA,EAKvD7K,SAAS8K,iBAAiB,QAASxV,KAAKyV,YAAa3O,GACrD4D,SAAS8K,iBAAiB,UAAWxV,KAAK0V,cAAe5O,GACzD4D,SAAS8K,iBAAiB,UAAWxV,KAAK2V,eAC1CjL,SAAS8K,iBAAiB,WAAYxV,KAAK4V,gBAEvC5V,KAAKqB,QAAQwU,8BACfnL,SAAS8K,iBAAiB,kBAAmBxV,KAAK8V,uBAGpDpL,SAAS8K,iBAAiB,SAAUxV,KAAK+V,aAC3C,CACF,GAEA,CAAA7V,IAAA,cAAAC,MAGA,SAAY6V,GACVhW,KAAKiW,kBAAkBD,GAEnBhW,KAAKqB,QAAQ6U,2BACflW,KAAKyO,iBAAiB0H,qBAAqBH,EAE/C,GAEA,CAAA9V,IAAA,gBAAAC,MAGA,SAAc6V,GACRhW,KAAKqB,QAAQ6U,2BACflW,KAAKyO,iBAAiB2H,uBAAuBJ,EAEjD,GAEA,CAAA9V,IAAA,gBAAAC,MAGA,SAAc6V,GACZhW,KAAKiW,kBAAkBD,EACzB,GAKA,CAAA9V,IAAA,iBAAAC,MACA,SAAe6V,GACbhW,KAAKiW,kBAAkBD,EACzB,GAKA,CAAA9V,IAAA,eAAAC,MACA,SAAa6V,GACXhW,KAAKiW,kBAAkBD,EACzB,GAKA,CAAA9V,IAAA,wBAAAC,MACA,SAAsB6V,GAKjBjS,UAAUsS,UAAUjW,SAAS,YAGhCJ,KAAKiW,kBAAkBD,EACzB,GAEA,CAAA9V,IAAA,oBAAAC,MAGA,SAAkB6V,GAAmC,IAC/CM,EAD+CC,EAAA,KAE/CP,EAAMrE,OAAO6E,UACfF,EAAgBN,EAAMrE,OAAO6E,QAAQtS,eAGvClE,KAAKL,UAAS,SAAC2C,GACb,IAAImU,EACFT,EAAMrE,SAAWrP,EAASkK,aACzBwJ,EAAMrE,QAAUrP,EAASkK,YAAYwG,SAASgD,EAAMrE,QAYvD,GANI4E,EAAKlV,QAAQqQ,oBAAsB/M,MAAMyD,QAAQ4N,EAAMU,QACzDD,EAAaT,EAAMU,KAAKC,MAAK,SAAC3B,GAAiB,IAAA4B,EAAA,OAC7C5B,SAAkB,QAAd4B,EAAJ5B,EAAM6B,oBAAY,IAAAD,OAAA,EAAlBA,EAAAnO,KAAAuM,EAAqB,kBAAkB,MAKtB,aAAlBsB,GACoB,UAAlBA,GACC,CAAC,OAAQ,SAAU,MAAO,MAAO,YAAYlW,SAC3C4V,EAAMrE,OAAOF,SAElBnP,EAASjB,QAAQyV,wBAClB,CAKA,IAAIC,EAAiBf,EAAMrE,OAAOoF,eAC9BC,EAAehB,EAAMrE,OAAOqF,aAE7B1U,EAASjB,QAAQkS,MAClBwD,EAAiBzU,EAASgH,UAAU2N,aAAaF,EAAgBzU,EAAS8N,YAC1E4G,EAAe1U,EAASgH,UAAU2N,aAAaD,EAAc1U,EAAS8N,aAGxE9N,EAASC,iBAAiBwU,EAAgBC,GAK1C1U,EAAS6O,mBAAqB6E,EAAMrE,OAEhCrP,EAASjB,QAAQW,OACnBC,QAAQC,IACN,aACAI,EAAS7C,mBACT6C,EAAS5C,sBACTsW,GAASA,EAAMrE,OAAO6E,QAAQtS,cAAa,IAAAxD,OACvC4B,EAASiK,iBAAgB,KAC7ByJ,aAAK,EAALA,EAAOvE,KAGb,MACGnP,EAASjB,QAAQyV,yBAA4BL,GAC9B,qBAAhBT,aAAK,EAALA,EAAOvE,QAKPnP,EAASC,iBAAiB,MAK1BD,EAAS6O,mBAAqB,KAE1B7O,EAASjB,QAAQW,OACnBC,QAAQC,IAAI,gCAADxB,OACuBsV,aAAK,EAALA,EAAOvE,KAAI,WAC3CuE,GAIR,GACF,GAEA,CAAA9V,IAAA,iBAAAC,MAGA,SAAe+W,GAAe,IAAAC,EAAA,KACvBD,GAELpW,OAAOyN,KAAKvO,KAAKkO,gBAAgB7F,SAAQ,SAAChB,GAAU,OAClD8P,EAAKjJ,eAAe7G,GAAYgB,QAAQ6O,EAAG,GAE/C,GAEA,CAAAhX,IAAA,UAAAC,MAGA,WACMH,KAAKqB,QAAQW,OACfC,QAAQC,IAAI,wCAADxB,OAC+BV,KAAKmO,sBAGjD,IAAAiJ,EAA4DpX,KAAKqB,QAAzDyF,wCAAAA,OAAuC,IAAAsQ,GAAQA,EAKvD1M,SAAS2M,oBAAoB,QAASrX,KAAKyV,YAAa3O,GACxD4D,SAAS2M,oBAAoB,UAAWrX,KAAK0V,cAAe5O,GAC5D4D,SAAS2M,oBAAoB,UAAWrX,KAAK2V,eAC7CjL,SAAS2M,oBAAoB,WAAYrX,KAAK4V,gBAC9ClL,SAAS2M,oBAAoB,SAAUrX,KAAK+V,cAIxC/V,KAAKqB,QAAQwU,8BACfnL,SAAS2M,oBAAoB,kBAAmBrX,KAAK8V,uBAGvDpL,SAASxB,YAAc,KACvBwB,SAAS4M,WAAa,KACtB5M,SAAS6M,cAAgB,KACzB7M,SAAS8M,UAAY,KAsBrBxX,KAAKiT,gBAjBgB,SAACnL,GAChBA,IACFA,EAAcU,cAAgB,KAC9BV,EAAcoB,YAAc,KAC5BpB,EAAc2P,gBAAkB,KAChC3P,EAAc0D,aAAe,KAC7B1D,EAAcwP,WAAa,KAC3BxP,EAAcyP,cAAgB,KAC9BzP,EAAc2D,QAAU,KACxB3D,EAAc4P,YAAc,KAC5B5P,EAAc0P,UAAY,KAE1B1P,EAAc4B,SACd5B,EAAgB,KAEpB,IAOA9H,KAAKwM,YAAYhE,cAAgB,KACjCxI,KAAKwM,YAAYhB,aAAe,KAChCxL,KAAKwM,YAAYkL,YAAc,KAK/B1X,KAAK2X,YAKD3X,KAAK0O,eACP1O,KAAK0O,aAAapE,UAClBtK,KAAK0O,aAAe,MAMtB1O,KAAKmR,mBAAqB,KAK1BnR,KAAKwM,YAAYvD,gBAAgB,mBAKjCjJ,KAAKwM,YAAYjB,UAAY,GAK5BzH,OAA6C,wBAAE9D,KAAKmO,qBAAuB,YACpErK,OAA6C,wBAAE9D,KAAKmO,qBAK5DnO,KAAK4X,aAAc,CACrB,GAEA,CAAA1X,IAAA,wBAAAC,MAGA,SAAsBL,GACpB,IAAMwU,EAActU,KAAKqB,QAAQiT,YAC7BuD,EAA0B,GA0B9B,OAxBIlT,MAAMyD,QAAQkM,IAChBA,EAAYjM,SAAQ,SAACyP,GACnB,GACEA,GACAA,EAAQ,OACkB,iBAAnBA,EAAQ,OACfA,EAAS3D,SACmB,iBAArB2D,EAAS3D,QAChB,CACA,IAAM4D,EAAkBD,EAAQ,MAAO1T,MAAM,KACrB0T,EAAS3D,QAAQ/P,MAAM,KAE3BhE,SAASN,KAC3B+X,EAAgB,GAAHnX,OAAAgE,EAAOmT,GAAanT,EAAKqT,IAE1C,MACE9V,QAAQ0K,KAAK,2DAEXmL,EAGN,IAGKD,CACT,GAEA,CAAA3X,IAAA,yBAAAC,MAGA,SAAuBL,EAAgBoU,GACrC,IAAM8D,EAAmBhY,KAAKqB,QAAQ2W,iBAElCrT,MAAMyD,QAAQ4P,IAChBA,EAAiB3P,SAAQ,SAAC4P,GAEtBA,EAAQC,WACqB,iBAAtBD,EAAQC,WACfD,EAAQ9X,OACiB,iBAAlB8X,EAAQ9X,OACf8X,EAAQ9D,SACmB,iBAApB8D,EAAQ9D,QAEQ8D,EAAQ9D,QAAQ/P,MAAM,KAE1BhE,SAASN,IAC1BoU,EAAS+D,EAAQC,UAAWD,EAAQ9X,OAGtC8B,QAAQ0K,KAAK,gEAEXsL,EAGN,GAEJ,GAAC,CAAA/X,IAAA,wBAAAC,MAED,WAIEH,KAAKmY,yBAKLnY,KAAKoY,yBACP,GAKA,CAAAlY,IAAA,0BAAAC,MACA,WACE2D,OAAOuU,cAAgB,SAACrC,GAAgC,IAAAsC,EACtD,GAA0B,QAA1BA,EAAItC,EAAMrE,OAAO9F,iBAAS,IAAAyM,GAAtBA,EAAwBtF,SAAS,aAGnC,OAFAgD,EAAMhP,iBACNgP,EAAM7D,mBACC,CAEX,CACF,GAEA,CAAAjS,IAAA,yBAAAC,MAGA,WACMH,KAAKqB,QAAQkX,qBACfvY,KAAKqB,QAAQ8J,gBAAiB,EAE1BnL,KAAKqB,QAAQW,OACfC,QAAQC,IAAI,sEAKlB,GAEA,CAAAhC,IAAA,SAAAC,MAGA,WACMH,KAAKqB,QAAQW,OACfC,QAAQC,IAAI,GAADxB,OAAIV,KAAKuM,iBAAgB,iBAMtCvM,KAAKwY,oBAE8B,mBAAxBxY,KAAKqB,QAAQoX,QAAuBzY,KAAKqB,QAAQoX,OAAOzY,KACrE,GAEA,CAAAE,IAAA,oBAAAC,MAGA,WAIMH,KAAKsJ,UAAUoP,iBACjB1Y,KAAK2Y,wBAGuC,mBAAnC3Y,KAAKqB,QAAQuX,mBACtB5Y,KAAKqB,QAAQuX,kBAAkB5Y,MAM/BA,KAAKwO,yBACLxO,KAAKsJ,UAAUuP,2BACd7Y,KAAKqB,QAAQ8J,iBACbnL,KAAKqB,QAAQyX,gBAEV9Y,KAAKqB,QAAQW,OACfC,QAAQC,IAAI,0DAOZlC,KAAKqB,QAAQ8J,gBACXnL,KAAKqB,QAAQW,OACfC,QAAQC,IACN,mEAIR,GAEA,CAAAhC,IAAA,eAAAC,MAGA,WAC2C,mBAA9BH,KAAKqB,QAAQ0X,cACtB/Y,KAAKqB,QAAQ0X,aAAa/Y,KAC9B,GAEA,CAAAE,IAAA,WAAAC,MAGA,WACuC,mBAA1BH,KAAKqB,QAAQ2X,UACtBhZ,KAAKqB,QAAQ2X,SAAShZ,KAC1B,GAEA,CAAAE,IAAA,kBAAAC,MAGA,WAC8C,mBAAjCH,KAAKqB,QAAQ4X,iBACtBjZ,KAAKqB,QAAQ4X,gBAAgBjZ,KACjC,GAAC,CAAAE,IAAA,cAAAC,MAcD,WAAc,IAAA+Y,EAAA,KACRvU,MAAMyD,QAAQpI,KAAKqB,QAAQ2L,WAC7BhN,KAAKqB,QAAQ2L,QAAQ3E,SAAQ,SAAC8Q,GAC5B,IAAMC,EAAiBF,EAAK5P,UAAU+P,cAAcF,GAClD,IAAIA,EAAeD,GAAQC,EAAeD,GAE5CE,EAAeE,MAAQF,EAAeE,KAAKJ,EAC7C,IAEAlZ,KAAK+N,sBAAwB,iBAE7B/N,KAAK2O,SACL3O,KAAKiZ,kBAET,GAEA,CAAA/Y,IAAA,gBAAAC,MAGA,SAAc2M,EAAcyM,GAC1B,QAAKvZ,KAAKgN,QAAQF,IAEX9M,KAAKgN,QAAQF,GAAMyM,EAC5B,GAEA,CAAArZ,IAAA,iBAAAC,MAGA,WACE,OAAOW,OAAOyN,KAAKvO,KAAKgN,QAC1B,GAEA,CAAA9M,IAAA,wBAAAC,MAGA,SACEqZ,EACAC,EACAC,EACAC,GACA,IAAAC,EAAA,KACMC,EAAclV,MAAMmV,KAAKN,EAAOO,UAClCC,EAAkB,EAyEtB,OAvEIH,EAAY9Z,QACd2Z,EAAsBrR,SAAQ,SAAC4R,EAAYC,GACzC,IAAMC,EAAWR,EAAoBO,GAMrC,KAAKC,GAAcA,EAAWF,GAC5B,OAAO,EAQT,IAAMG,EAAqBH,EAAaD,EAClCK,EAAmBF,EAAWH,EAK9BM,EAAe5P,SAASC,cAAc,OAC5C2P,EAAa1P,WAAa,sBAC1B,IAAM2P,EAAe,GAAH7Z,OAAMkZ,EAAKvY,QAAQqM,WAAU,MAAAhN,OAAK+Y,EAAQ,KAAA/Y,OAAIwZ,GAChEI,EAAarG,aAAa,aAAcsG,GAKxC,IAAMC,EAAoBX,EAAY5E,OACpCmF,EACAC,EAAmBD,EAAqB,GAE1CJ,GAAmBK,EAAmBD,EAKtCI,EAAkBnS,SAAQ,SAACoS,GAAO,OAChCH,EAAa5O,YAAY+O,EAAQ,IAMnCZ,EAAY5E,OAAOmF,EAAoB,EAAGE,GAK1Cd,EAAOjO,UAAY,GAKnBsO,EAAYxR,SAAQ,SAACoS,GAAO,OAAKjB,EAAO9N,YAAY+O,EAAQ,IAExDb,EAAKvY,QAAQW,OACfC,QAAQC,IACN,kBACAsY,EACAJ,EACAC,EACAL,EAAkB,EAGxB,IAGKR,CACT,GAAC,CAAAtZ,IAAA,SAAAC,MAgBD,WAAS,IAAAua,EAAA,KAIP1a,KAAK2X,YAKA3X,KAAK4X,aACR5X,KAAK4Y,oBAMP5Y,KAAK+Y,eAEL,IAAM4B,EAAc,aAAHja,OAAgBV,KAAKqB,QAAQqM,YACxCkN,EAAS5a,KAAKqB,QAAQuZ,QCjuDvB,CACLC,QAAS,CACP,mCACA,mCACA,uCACA,sCACA,kBAEFC,MAAO,CACL,mCACA,kCACA,uCACA,sCACA,mBDqtDI3P,EAAiBnL,KAAKqB,QAAQ8J,iBAAkB,EAChD4P,EAAsB5P,EAAiB,kBAAoB,GAC3D2N,EAAiB9Y,KAAKqB,QAAQyX,iBAAkB,EAChDkC,EAA6Bhb,KAAKqB,QAAQ2Z,2BAKhDhb,KAAKwM,YAAY5B,UAAY5K,KAAKib,uBAChCjb,KAAKqB,QAAQsM,MACbgN,EACA3a,KAAK+N,sBACLgN,GAMF/a,KAAKwM,YAAYyH,aAAa,kBAAmBjU,KAAKmO,qBAKtDnO,KAAKgU,gBAAkBtJ,SAASC,cAAc,OAC9C3K,KAAKgU,gBAAgBpJ,UAAY,UAKjCgQ,EAAO5a,KAAKqB,QAAQqM,YAAc1N,KAAKiO,aAAa5F,SAClD,SAAC6S,EAAaC,GACZ,IAAIC,EAAWF,EAAI9W,MAAM,KAMvBsW,EAAKrZ,QAAQyM,mBACb4M,EAAKrZ,QAAQyM,kBACX4M,EAAKrZ,QAAQqM,YAAcgN,EAAKzM,eAGlCmN,EAAWA,EAAShO,QAClB,SAAC/F,GAAU,OACTqT,EAAKrZ,QAAQyM,oBACZ4M,EAAKrZ,QAAQyM,kBACZ4M,EAAKrZ,QAAQqM,YAAcgN,EAAKzM,aAChC7N,SAASiH,EAAW,KAO5B,IAAImS,EAAS9O,SAASC,cAAc,OACpC6O,EAAO5O,WAAa,SAKpB,IAAM8O,EAAkC,GAClCC,EAAgC,GAKtCyB,EAAS/S,SAAQ,SAACvI,EAAQub,GAAW,IAAAC,EAI7BC,GACHP,GACiB,iBAAXlb,GACPA,EAAOC,OAAS,GACQ,IAAxBD,EAAOsF,QAAQ,KAEXoW,GACHR,GACiB,iBAAXlb,GACPA,EAAOC,OAAS,GAChBD,EAAOsF,QAAQ,OAAStF,EAAOC,OAAS,EAKtCwb,IACF7B,EAAsBjF,KAAK4G,GAK3Bvb,EAASA,EAAOU,QAAQ,MAAO,KAG7Bgb,IACF7B,EAAoBlF,KAAK4G,GAKzBvb,EAASA,EAAOU,QAAQ,MAAO,KAMjC,IAAMib,EAAcf,EAAKpR,UAAUoS,eAAe5b,GAC5C6b,EAAoBjB,EAAKpR,UAAUsS,qBACvC9b,EACA4a,EAAKrZ,QAAQV,QACb+Z,EAAKrZ,QAAQwa,cAMTC,EAAapB,EAAKrZ,QAAQ0a,aAAe,SAAW,MACpD3U,EAAYsD,SAASC,cAAcmR,GACzC1U,EAAUwD,WAAa,aAAJlK,OAAiB+a,IAKpCH,EAAAlU,EAAUyE,WAAUC,IAAGrK,MAAA6Z,EAAA5W,EAAIgW,EAAKsB,sBAAsBlc,KAKtD4a,EAAKuB,uBACHnc,GACA,SAACoY,EAAmB/X,GAClBiH,EAAU6M,aAAaiE,EAAW/X,EACpC,IAGFua,EAAKtI,kBAAoB,mBAOvBsI,EAAKpR,UAAUuP,0BACd1N,GACA2N,EAmBG3N,GAIF/D,EAAUoE,aAAe,SAAC/F,GACxBiV,EAAK7R,oBAAoB/I,EAAQ2F,GACjCiV,EAAKwB,sBAAsBpc,EAAQ2F,EACrC,EACA2B,EAAUkQ,WAAa,SAAC7R,GACtBiV,EAAKyB,oBAAoBrc,EAAQ2F,EACnC,EACA2B,EAAUmQ,cAAgB,SAAC9R,GACzBiV,EAAKyB,oBAAoBrc,EAAQ2F,EACnC,IAKA2B,EAAUqE,QAAU,SAAChG,GACnBiV,EAAKlI,cAAa,GAOsB,mBAA/BkI,EAAKrZ,QAAQ6R,eAClBwH,EAAKrZ,QAAQyX,gBAAkB4B,EAAKrZ,QAAQ+a,kBAE9C1B,EAAK7R,oBAAoB/I,EAAQ2F,EAErC,EACA2B,EAAUsQ,YAAc,SAACjS,IAMmB,mBAA/BiV,EAAKrZ,QAAQ6R,eACnBwH,EAAKrZ,QAAQyX,gBAAkB4B,EAAKrZ,QAAQ+a,oBAE9C1B,EAAK1I,aAEN0I,EAAK7R,oBAAoB/I,EAAQ2F,GAEnCiV,EAAKwB,sBAAsBpc,EAAQ2F,EACrC,EACA2B,EAAUoQ,UAAY,SAAC/R,GACrBiV,EAAKyB,oBAAoBrc,EAAQ2F,EACnC,IA/DF2B,EAAUoB,cAAgB,SAAC/C,GACzBiV,EAAK7R,oBAAoB/I,EAAQ2F,GACjCiV,EAAKwB,sBAAsBpc,EAAQ2F,EACrC,EACA2B,EAAU8B,YAAc,SAACzD,GACvBiV,EAAKyB,oBAAoBrc,EAAQ2F,EACnC,EACA2B,EAAUqQ,gBAAkB,SAAChS,GAC3BiV,EAAKyB,oBAAoBrc,EAAQ2F,EACnC,GA6DF2B,EAAU6M,aAAa,aAAcnU,GAMrC,IAAMuc,EAAY,GAAH3b,OAAMga,EAAKrZ,QAAQqM,WAAU,MAAAhN,OAAKya,EAAM,KAAAza,OAAI2a,GAC3DjU,EAAU6M,aAAa,gBAAiBoI,GAKxC,IAAMC,EAAgB5R,SAASC,cAAc,QAC7C2R,EAAc/Q,UAAYoQ,EAC1BvU,EAAUsE,YAAY4Q,GAKjB5B,EAAKxM,eAAepO,KAAS4a,EAAKxM,eAAepO,GAAU,IAEhE4a,EAAKxM,eAAepO,GAAQ2U,KAAKrN,GAKjCoS,EAAO9N,YAAYtE,EACrB,IAKAoS,EAASkB,EAAK6B,sBACZ/C,EACA2B,EACAzB,EACAC,GAMFe,EAAK1G,gBAAgBtI,YAAY8N,EACnC,IAMFxZ,KAAKwM,YAAYd,YAAY1L,KAAKgU,iBAKlChU,KAAKgZ,WAEAhZ,KAAK4X,cAIR5X,KAAK4X,aAAc,GAOjB5X,KAAKsJ,UAAUuP,0BACd1N,GACA2N,EAMQ3N,GAITT,SAAS4M,WAAa,SAAC7R,GAAuB,OAC5CiV,EAAKyB,yBAAoBtb,EAAW4E,EAAE,EACxCiF,SAAS6M,cAAgB,SAAC9R,GAAuB,OAC/CiV,EAAKyB,yBAAoBtb,EAAW4E,EAAE,EAExCzF,KAAKwM,YAAYhB,aAAe,SAAC/F,GAAuB,OACtDiV,EAAK8B,iCAAiC/W,EAAE,GAChC0F,IAIVT,SAAS8M,UAAY,SAAC/R,GAAuB,OAC3CiV,EAAKyB,yBAAoBtb,EAAW4E,EAAE,EACxCzF,KAAKwM,YAAYkL,YAAc,SAACjS,GAAuB,OACrDiV,EAAK8B,iCAAiC/W,EAAE,IAtB1CiF,SAASxB,YAAc,SAACzD,GAAuB,OAC7CiV,EAAKyB,yBAAoBtb,EAAW4E,EAAE,EACxCzF,KAAKwM,YAAYhE,cAAgB,SAAC/C,GAAuB,OACvDiV,EAAK8B,iCAAiC/W,EAAE,GAyB5CzF,KAAKyY,SAET,I,4FAAC,CA/gEiB,GEpBpB,W", "sources": ["webpack://simple-keyboard/./src/lib/services/Utilities.ts", "webpack://simple-keyboard/./src/lib/services/PhysicalKeyboard.ts", "webpack://simple-keyboard/./src/lib/components/CandidateBox.ts", "webpack://simple-keyboard/./src/lib/components/Keyboard.ts", "webpack://simple-keyboard/./src/lib/services/KeyboardLayout.ts", "webpack://simple-keyboard/./src/lib/index.modern.ts"], "sourcesContent": ["import { KeyboardInput } from \"./../interfaces\";\nimport { KeyboardOptions, UtilitiesParams } from \"../interfaces\";\n\n/**\n * Utility Service\n */\nclass Utilities {\n  getOptions: () => KeyboardOptions;\n  getCaretPosition: () => number | null;\n  getCaretPositionEnd: () => number | null;\n  dispatch: any;\n  maxLengthReached!: boolean;\n\n  /**\n   * Creates an instance of the Utility service\n   */\n  constructor({\n    getOptions,\n    getCaretPosition,\n    getCaretPositionEnd,\n    dispatch,\n  }: UtilitiesParams) {\n    this.getOptions = getOptions;\n    this.getCaretPosition = getCaretPosition;\n    this.getCaretPositionEnd = getCaretPositionEnd;\n    this.dispatch = dispatch;\n\n    /**\n     * Bindings\n     */\n    Utilities.bindMethods(Utilities, this);\n  }\n\n  /**\n   * Retrieve button type\n   *\n   * @param  {string} button The button's layout name\n   * @return {string} The button type\n   */\n  getButtonType(button: string): string {\n    return button.includes(\"{\") && button.includes(\"}\") && button !== \"{//}\"\n      ? \"functionBtn\"\n      : \"standardBtn\";\n  }\n\n  /**\n   * Adds default classes to a given button\n   *\n   * @param  {string} button The button's layout name\n   * @return {string} The classes to be added to the button\n   */\n  getButtonClass(button: string): string {\n    const buttonTypeClass = this.getButtonType(button);\n    const buttonWithoutBraces = button.replace(\"{\", \"\").replace(\"}\", \"\");\n    let buttonNormalized = \"\";\n\n    if (buttonTypeClass !== \"standardBtn\")\n      buttonNormalized = ` hg-button-${buttonWithoutBraces}`;\n\n    return `hg-${buttonTypeClass}${buttonNormalized}`;\n  }\n\n  /**\n   * Default button display labels\n   */\n  getDefaultDiplay() {\n    return {\n      \"{bksp}\": \"backspace\",\n      \"{backspace}\": \"backspace\",\n      \"{enter}\": \"< enter\",\n      \"{shift}\": \"shift\",\n      \"{shiftleft}\": \"shift\",\n      \"{shiftright}\": \"shift\",\n      \"{alt}\": \"alt\",\n      \"{s}\": \"shift\",\n      \"{tab}\": \"tab\",\n      \"{lock}\": \"caps\",\n      \"{capslock}\": \"caps\",\n      \"{accept}\": \"Submit\",\n      \"{space}\": \" \",\n      \"{//}\": \" \",\n      \"{esc}\": \"esc\",\n      \"{escape}\": \"esc\",\n      \"{f1}\": \"f1\",\n      \"{f2}\": \"f2\",\n      \"{f3}\": \"f3\",\n      \"{f4}\": \"f4\",\n      \"{f5}\": \"f5\",\n      \"{f6}\": \"f6\",\n      \"{f7}\": \"f7\",\n      \"{f8}\": \"f8\",\n      \"{f9}\": \"f9\",\n      \"{f10}\": \"f10\",\n      \"{f11}\": \"f11\",\n      \"{f12}\": \"f12\",\n      \"{numpaddivide}\": \"/\",\n      \"{numlock}\": \"lock\",\n      \"{arrowup}\": \"↑\",\n      \"{arrowleft}\": \"←\",\n      \"{arrowdown}\": \"↓\",\n      \"{arrowright}\": \"→\",\n      \"{prtscr}\": \"print\",\n      \"{scrolllock}\": \"scroll\",\n      \"{pause}\": \"pause\",\n      \"{insert}\": \"ins\",\n      \"{home}\": \"home\",\n      \"{pageup}\": \"up\",\n      \"{delete}\": \"del\",\n      \"{forwarddelete}\": \"del\",\n      \"{end}\": \"end\",\n      \"{pagedown}\": \"down\",\n      \"{numpadmultiply}\": \"*\",\n      \"{numpadsubtract}\": \"-\",\n      \"{numpadadd}\": \"+\",\n      \"{numpadenter}\": \"enter\",\n      \"{period}\": \".\",\n      \"{numpaddecimal}\": \".\",\n      \"{numpad0}\": \"0\",\n      \"{numpad1}\": \"1\",\n      \"{numpad2}\": \"2\",\n      \"{numpad3}\": \"3\",\n      \"{numpad4}\": \"4\",\n      \"{numpad5}\": \"5\",\n      \"{numpad6}\": \"6\",\n      \"{numpad7}\": \"7\",\n      \"{numpad8}\": \"8\",\n      \"{numpad9}\": \"9\",\n    };\n  }\n  /**\n   * Returns the display (label) name for a given button\n   *\n   * @param  {string} button The button's layout name\n   * @param  {object} display The provided display option\n   * @param  {boolean} mergeDisplay Whether the provided param value should be merged with the default one.\n   */\n  getButtonDisplayName(\n    button: string,\n    display: KeyboardOptions[\"display\"],\n    mergeDisplay = false\n  ) {\n    if (mergeDisplay) {\n      display = Object.assign({}, this.getDefaultDiplay(), display);\n    } else {\n      display = display || this.getDefaultDiplay();\n    }\n\n    return display[button] || button;\n  }\n\n  /**\n   * Returns the updated input resulting from clicking a given button\n   *\n   * @param  {string} button The button's layout name\n   * @param  {string} input The input string\n   * @param  {number} caretPos The cursor's current position\n   * @param  {number} caretPosEnd The cursor's current end position\n   * @param  {boolean} moveCaret Whether to update simple-keyboard's cursor\n   */\n  getUpdatedInput(\n    button: string,\n    input: string,\n    caretPos: any,\n    caretPosEnd = caretPos,\n    moveCaret = false\n  ) {\n    const options = this.getOptions();\n    const commonParams: [number | undefined, number | undefined, boolean] = [\n      caretPos,\n      caretPosEnd,\n      moveCaret,\n    ];\n\n    let output = input;\n\n    if (\n      (button === \"{bksp}\" || button === \"{backspace}\") &&\n      output.length > 0\n    ) {\n      output = this.removeAt(output, ...commonParams);\n    } else if (\n      (button === \"{delete}\" || button === \"{forwarddelete}\") &&\n      output.length > 0\n    ) {\n      output = this.removeForwardsAt(output, ...commonParams);\n    } else if (button === \"{space}\")\n      output = this.addStringAt(output, \" \", ...commonParams);\n    else if (\n      button === \"{tab}\" &&\n      !(\n        typeof options.tabCharOnTab === \"boolean\" &&\n        options.tabCharOnTab === false\n      )\n    ) {\n      output = this.addStringAt(output, \"\\t\", ...commonParams);\n    } else if (\n      (button === \"{enter}\" || button === \"{numpadenter}\") &&\n      options.newLineOnEnter\n    )\n      output = this.addStringAt(output, \"\\n\", ...commonParams);\n    else if (\n      button.includes(\"numpad\") &&\n      Number.isInteger(Number(button[button.length - 2]))\n    ) {\n      output = this.addStringAt(\n        output,\n        button[button.length - 2],\n        ...commonParams\n      );\n    } else if (button === \"{numpaddivide}\")\n      output = this.addStringAt(output, \"/\", ...commonParams);\n    else if (button === \"{numpadmultiply}\")\n      output = this.addStringAt(output, \"*\", ...commonParams);\n    else if (button === \"{numpadsubtract}\")\n      output = this.addStringAt(output, \"-\", ...commonParams);\n    else if (button === \"{numpadadd}\")\n      output = this.addStringAt(output, \"+\", ...commonParams);\n    else if (button === \"{numpaddecimal}\")\n      output = this.addStringAt(output, \".\", ...commonParams);\n    else if (button === \"{\" || button === \"}\")\n      output = this.addStringAt(output, button, ...commonParams);\n    else if (!button.includes(\"{\") && !button.includes(\"}\"))\n      output = this.addStringAt(output, button, ...commonParams);\n\n    if(options.debug){\n      console.log(\"Input will be: \"+ output);\n    }\n\n    return output;\n  }\n\n  /**\n   * Moves the cursor position by a given amount\n   *\n   * @param  {number} length Represents by how many characters the input should be moved\n   * @param  {boolean} minus Whether the cursor should be moved to the left or not.\n   */\n  updateCaretPos(length: number, minus = false) {\n    const newCaretPos = this.updateCaretPosAction(length, minus);\n\n    this.dispatch((instance: any) => {\n      instance.setCaretPosition(newCaretPos);\n    });\n  }\n\n  /**\n   * Action method of updateCaretPos\n   *\n   * @param  {number} length Represents by how many characters the input should be moved\n   * @param  {boolean} minus Whether the cursor should be moved to the left or not.\n   */\n  updateCaretPosAction(length: number, minus = false) {\n    const options = this.getOptions();\n    let caretPosition = this.getCaretPosition();\n\n    if (caretPosition != null) {\n      if (minus) {\n        if (caretPosition > 0) caretPosition = caretPosition - length;\n      } else {\n        caretPosition = caretPosition + length;\n      }\n    }\n\n    if (options.debug) {\n      console.log(\"Caret at:\", caretPosition);\n    }\n\n    return caretPosition;\n  }\n\n  /**\n   * Adds a string to the input at a given position\n   *\n   * @param  {string} source The source input\n   * @param  {string} str The string to add\n   * @param  {number} position The (cursor) position where the string should be added\n   * @param  {boolean} moveCaret Whether to update simple-keyboard's cursor\n   */\n  addStringAt(\n    source: string,\n    str: string,\n    position = source.length,\n    positionEnd = source.length,\n    moveCaret = false\n  ) {\n    let output;\n\n    if (!position && position !== 0) {\n      output = source + str;\n    } else {\n      output = [source.slice(0, position), str, source.slice(positionEnd)].join(\n        \"\"\n      );\n\n      /**\n       * Avoid caret position change when maxLength is set\n       */\n      if (!this.isMaxLengthReached()) {\n        if (moveCaret) this.updateCaretPos(str.length);\n      }\n    }\n\n    return output;\n  }\n\n  /**\n   * Check whether the button is a standard button\n   */\n  isStandardButton = (button: string) =>\n    button && !(button[0] === \"{\" && button[button.length - 1] === \"}\");\n\n  /**\n   * Removes an amount of characters before a given position\n   *\n   * @param  {string} source The source input\n   * @param  {number} position The (cursor) position from where the characters should be removed\n   * @param  {boolean} moveCaret Whether to update simple-keyboard's cursor\n   */\n  removeAt(\n    source: string,\n    position = source.length,\n    positionEnd = source.length,\n    moveCaret = false\n  ) {\n    if (position === 0 && positionEnd === 0) {\n      return source;\n    }\n\n    let output;\n\n    if (position === positionEnd) {\n      let prevTwoChars;\n      let emojiMatched;\n      const emojiMatchedReg = /([\\uD800-\\uDBFF][\\uDC00-\\uDFFF])/g;\n\n      /**\n       * Emojis are made out of two characters, so we must take a custom approach to trim them.\n       * For more info: https://mathiasbynens.be/notes/javascript-unicode\n       */\n      if (position && position >= 0) {\n        prevTwoChars = source.substring(position - 2, position);\n        emojiMatched = prevTwoChars.match(emojiMatchedReg);\n\n        if (emojiMatched) {\n          output = source.substr(0, position - 2) + source.substr(position);\n          if (moveCaret) this.updateCaretPos(2, true);\n        } else {\n          output = source.substr(0, position - 1) + source.substr(position);\n          if (moveCaret) this.updateCaretPos(1, true);\n        }\n      } else {\n        prevTwoChars = source.slice(-2);\n        emojiMatched = prevTwoChars.match(emojiMatchedReg);\n\n        if (emojiMatched) {\n          output = source.slice(0, -2);\n          if (moveCaret) this.updateCaretPos(2, true);\n        } else {\n          output = source.slice(0, -1);\n          if (moveCaret) this.updateCaretPos(1, true);\n        }\n      }\n    } else {\n      output = source.slice(0, position) + source.slice(positionEnd);\n      if (moveCaret) {\n        this.dispatch((instance: any) => {\n          instance.setCaretPosition(position);\n        });\n      }\n    }\n\n    return output;\n  }\n\n  /**\n   * Removes an amount of characters after a given position\n   *\n   * @param  {string} source The source input\n   * @param  {number} position The (cursor) position from where the characters should be removed\n   */\n  removeForwardsAt(\n    source: string,\n    position: number = source.length,\n    positionEnd: number = source.length,\n    moveCaret = false\n  ) {\n    if (!source?.length || position === null) {\n      return source;\n    }\n\n    let output;\n\n    if (position === positionEnd) {\n      const emojiMatchedReg = /([\\uD800-\\uDBFF][\\uDC00-\\uDFFF])/g;\n\n      /**\n       * Emojis are made out of two characters, so we must take a custom approach to trim them.\n       * For more info: https://mathiasbynens.be/notes/javascript-unicode\n       */\n      const nextTwoChars = source.substring(position, position + 2);\n      const emojiMatched = nextTwoChars.match(emojiMatchedReg);\n\n      if (emojiMatched) {\n        output = source.substr(0, position) + source.substr(position + 2);\n      } else {\n        output = source.substr(0, position) + source.substr(position + 1);\n      }\n    } else {\n      output = source.slice(0, position) + source.slice(positionEnd);\n      if (moveCaret) {\n        this.dispatch((instance: any) => {\n          instance.setCaretPosition(position);\n        });\n      }\n    }\n\n    return output;\n  }\n\n  /**\n   * Determines whether the maxLength has been reached. This function is called when the maxLength option it set.\n   *\n   * @param  {object} inputObj\n   * @param  {string} updatedInput\n   */\n  handleMaxLength(inputObj: KeyboardInput, updatedInput: string) {\n    const options = this.getOptions();\n    const maxLength = options.maxLength;\n    const currentInput = inputObj[options.inputName || \"default\"];\n    const condition = updatedInput.length - 1 >= maxLength;\n\n    if (\n      /**\n       * If pressing this button won't add more characters\n       * We exit out of this limiter function\n       */\n      updatedInput.length <= currentInput.length\n    ) {\n      return false;\n    }\n\n    if (Number.isInteger(maxLength)) {\n      if (options.debug) {\n        console.log(\"maxLength (num) reached:\", condition);\n      }\n\n      if (condition) {\n        /**\n         * @type {boolean} Boolean value that shows whether maxLength has been reached\n         */\n        this.maxLengthReached = true;\n        return true;\n      } else {\n        this.maxLengthReached = false;\n        return false;\n      }\n    }\n\n    if (typeof maxLength === \"object\") {\n      const condition =\n        updatedInput.length - 1 >= maxLength[options.inputName || \"default\"];\n\n      if (options.debug) {\n        console.log(\"maxLength (obj) reached:\", condition);\n      }\n\n      if (condition) {\n        this.maxLengthReached = true;\n        return true;\n      } else {\n        this.maxLengthReached = false;\n        return false;\n      }\n    }\n  }\n\n  /**\n   * Gets the current value of maxLengthReached\n   */\n  isMaxLengthReached() {\n    return Boolean(this.maxLengthReached);\n  }\n\n  /**\n   * Determines whether a touch device is being used\n   */\n  isTouchDevice() {\n    return \"ontouchstart\" in window || navigator.maxTouchPoints;\n  }\n\n  /**\n   * Determines whether pointer events are supported\n   */\n  pointerEventsSupported() {\n    return !!window.PointerEvent;\n  }\n\n  /**\n   * Bind all methods in a given class\n   */\n\n  static bindMethods(myClass: any, instance: any) {\n    // eslint-disable-next-line no-unused-vars\n    for (const myMethod of Object.getOwnPropertyNames(myClass.prototype)) {\n      const excludeMethod =\n        myMethod === \"constructor\" || myMethod === \"bindMethods\";\n      if (!excludeMethod) {\n        instance[myMethod] = instance[myMethod].bind(instance);\n      }\n    }\n  }\n\n  /**\n   * Transforms an arbitrary string to camelCase\n   *\n   * @param  {string} str The string to transform.\n   */\n  camelCase(str: string): string {\n    if (!str) return \"\";\n\n    return str\n      .toLowerCase()\n      .trim()\n      .split(/[.\\-_\\s]/g)\n      .reduce((str, word) =>\n        word.length ? str + word[0].toUpperCase() + word.slice(1) : str\n      );\n  }\n\n  /**\n   * Split array into chunks\n   */\n  chunkArray<T>(arr: T[], size: number): T[][] {\n    return [...Array(Math.ceil(arr.length / size))].map((_, i) =>\n      arr.slice(size * i, size + size * i)\n    );\n  }\n\n  /**\n   * Escape regex input\n   */\n  escapeRegex(str: string) {\n    return str.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\");\n  }\n\n  /**\n   * Calculate caret position offset when using rtl option\n   */\n  getRtlOffset(index: number, input: string) {\n    let newIndex = index;\n    const startMarkerIndex = input.indexOf(\"\\u202B\");\n    const endMarkerIndex = input.indexOf(\"\\u202C\");\n\n    if(startMarkerIndex < index && startMarkerIndex != -1){ newIndex--; }\n    if(endMarkerIndex < index && startMarkerIndex != -1){ newIndex--; }\n\n    return newIndex < 0 ? 0 : newIndex;\n  }\n\n  /**\n   * Reusable empty function\n   */\n  static noop = () => {};\n\n  /**\n   * Check if a function is a constructor\n   */\n  isConstructor(f: any) {\n    try {\n      Reflect.construct(String, [], f);\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }\n}\n\nexport default Utilities;\n", "import { KeyboardOptions, PhysicalKeyboardParams } from \"../interfaces\";\nimport Utilities from \"../services/Utilities\";\n\n/**\n * Physical Keyboard Service\n */\nclass PhysicalKeyboard {\n  getOptions: () => KeyboardOptions;\n  dispatch: any;\n\n  /**\n   * Creates an instance of the PhysicalKeyboard service\n   */\n  constructor({ dispatch, getOptions }: PhysicalKeyboardParams) {\n    /**\n     * @type {object} A simple-keyboard instance\n     */\n    this.dispatch = dispatch;\n    this.getOptions = getOptions;\n\n    /**\n     * Bindings\n     */\n    Utilities.bindMethods(PhysicalKeyboard, this);\n  }\n\n  handleHighlightKeyDown(e: KeyboardEvent) {\n    const options = this.getOptions();\n\n    if(options.physicalKeyboardHighlightPreventDefault && this.isModifierKey(e)){\n      e.preventDefault();\n      e.stopImmediatePropagation();\n    }\n\n    const buttonPressed = this.getSimpleKeyboardLayoutKey(e);\n\n    this.dispatch((instance: any) => {\n      const standardButtonPressed = instance.getButtonElement(buttonPressed);\n      const functionButtonPressed = instance.getButtonElement(\n        `{${buttonPressed}}`\n      );\n      let buttonDOM;\n      let buttonName: string;\n\n      if (standardButtonPressed) {\n        buttonDOM = standardButtonPressed;\n        buttonName = buttonPressed;\n      } else if (functionButtonPressed) {\n        buttonDOM = functionButtonPressed;\n        buttonName = `{${buttonPressed}}`;\n      } else {\n        return;\n      }\n\n      const applyButtonStyle = (buttonElement: HTMLElement) => {\n        buttonElement.style.background =\n          options.physicalKeyboardHighlightBgColor || \"#dadce4\";\n          buttonElement.style.color =\n          options.physicalKeyboardHighlightTextColor || \"black\";\n      }\n\n      if (buttonDOM) {\n        if(Array.isArray(buttonDOM)){\n          buttonDOM.forEach(buttonElement => applyButtonStyle(buttonElement));\n\n          // Even though we have an array of buttons, we just want to press one of them\n          if (options.physicalKeyboardHighlightPress) {\n            if (options.physicalKeyboardHighlightPressUsePointerEvents) {\n              buttonDOM[0]?.onpointerdown?.(e);\n            } else if (options.physicalKeyboardHighlightPressUseClick) {\n              buttonDOM[0]?.click();\n            } else {\n              instance.handleButtonClicked(buttonName, e);\n            }\n          }\n        } else {\n          applyButtonStyle(buttonDOM);\n\n          if (options.physicalKeyboardHighlightPress) {\n            if (options.physicalKeyboardHighlightPressUsePointerEvents) {\n              buttonDOM?.onpointerdown?.(e);\n            } else if (options.physicalKeyboardHighlightPressUseClick) {\n              buttonDOM.click();\n            } else {\n              instance.handleButtonClicked(buttonName, e);\n            }\n          }\n        }\n      }\n    });\n  }\n\n  handleHighlightKeyUp(e: KeyboardEvent) {\n    const options = this.getOptions();\n\n    if(options.physicalKeyboardHighlightPreventDefault && this.isModifierKey(e)){\n      e.preventDefault();\n      e.stopImmediatePropagation();\n    }\n    \n    const buttonPressed = this.getSimpleKeyboardLayoutKey(e);\n\n    this.dispatch((instance: any) => {\n      const buttonDOM =\n        instance.getButtonElement(buttonPressed) ||\n        instance.getButtonElement(`{${buttonPressed}}`);\n\n      const applyButtonStyle = (buttonElement: HTMLElement) => {\n        if(buttonElement.removeAttribute){\n          buttonElement.removeAttribute(\"style\");\n        }\n      };\n\n      if (buttonDOM) {\n        if(Array.isArray(buttonDOM)){\n          buttonDOM.forEach(buttonElement => applyButtonStyle(buttonElement));\n\n          // Even though we have an array of buttons, we just want to press one of them\n          if (options.physicalKeyboardHighlightPressUsePointerEvents) {\n            buttonDOM[0]?.onpointerup?.(e);\n          }\n        } else {\n          applyButtonStyle(buttonDOM);\n\n          if (options.physicalKeyboardHighlightPressUsePointerEvents) {\n            buttonDOM?.onpointerup?.(e);\n          }\n        }\n      }\n    });\n  }\n\n  /**\n   * Transforms a KeyboardEvent's \"key.code\" string into a simple-keyboard layout format\n   * @param  {object} e The KeyboardEvent\n   */\n  getSimpleKeyboardLayoutKey(e: KeyboardEvent) {\n    let output = \"\";\n    const keyId = e.code || e.key || this.keyCodeToKey(e?.keyCode);\n\n    if (\n      keyId?.includes(\"Numpad\") ||\n      keyId?.includes(\"Shift\") ||\n      keyId?.includes(\"Space\") ||\n      keyId?.includes(\"Backspace\") ||\n      keyId?.includes(\"Control\") ||\n      keyId?.includes(\"Alt\") ||\n      keyId?.includes(\"Meta\")\n    ) {\n      output = e.code || \"\";\n    } else {\n      output = e.key || this.keyCodeToKey(e?.keyCode) || \"\";\n    }\n\n    return output.length > 1 ? output?.toLowerCase() : output;\n  }\n\n  /**\n   * Retrieve key from keyCode\n   */\n  keyCodeToKey(keyCode: number): string {\n    return {\n      8: \"Backspace\",\n      9: \"Tab\",\n      13: \"Enter\",\n      16: \"Shift\",\n      17: \"Ctrl\",\n      18: \"Alt\",\n      19: \"Pause\",\n      20: \"CapsLock\",\n      27: \"Esc\",\n      32: \"Space\",\n      33: \"PageUp\",\n      34: \"PageDown\",\n      35: \"End\",\n      36: \"Home\",\n      37: \"ArrowLeft\",\n      38: \"ArrowUp\",\n      39: \"ArrowRight\",\n      40: \"ArrowDown\",\n      45: \"Insert\",\n      46: \"Delete\",\n      48: \"0\",\n      49: \"1\",\n      50: \"2\",\n      51: \"3\",\n      52: \"4\",\n      53: \"5\",\n      54: \"6\",\n      55: \"7\",\n      56: \"8\",\n      57: \"9\",\n      65: \"A\",\n      66: \"B\",\n      67: \"C\",\n      68: \"D\",\n      69: \"E\",\n      70: \"F\",\n      71: \"G\",\n      72: \"H\",\n      73: \"I\",\n      74: \"J\",\n      75: \"K\",\n      76: \"L\",\n      77: \"M\",\n      78: \"N\",\n      79: \"O\",\n      80: \"P\",\n      81: \"Q\",\n      82: \"R\",\n      83: \"S\",\n      84: \"T\",\n      85: \"U\",\n      86: \"V\",\n      87: \"W\",\n      88: \"X\",\n      89: \"Y\",\n      90: \"Z\",\n      91: \"Meta\",\n      96: \"Numpad0\",\n      97: \"Numpad1\",\n      98: \"Numpad2\",\n      99: \"Numpad3\",\n      100: \"Numpad4\",\n      101: \"Numpad5\",\n      102: \"Numpad6\",\n      103: \"Numpad7\",\n      104: \"Numpad8\",\n      105: \"Numpad9\",\n      106: \"NumpadMultiply\",\n      107: \"NumpadAdd\",\n      109: \"NumpadSubtract\",\n      110: \"NumpadDecimal\",\n      111: \"NumpadDivide\",\n      112: \"F1\",\n      113: \"F2\",\n      114: \"F3\",\n      115: \"F4\",\n      116: \"F5\",\n      117: \"F6\",\n      118: \"F7\",\n      119: \"F8\",\n      120: \"F9\",\n      121: \"F10\",\n      122: \"F11\",\n      123: \"F12\",\n      144: \"NumLock\",\n      145: \"ScrollLock\",\n      186: \";\",\n      187: \"=\",\n      188: \",\",\n      189: \"-\",\n      190: \".\",\n      191: \"/\",\n      192: \"`\",\n      219: \"[\",\n      220: \"\\\\\",\n      221: \"]\",\n      222: \"'\",\n    }[keyCode] || \"\";\n  }\n\n  isModifierKey = (e: KeyboardEvent): boolean => {\n    return (\n      e.altKey\n      || e.ctrlKey\n      || e.shiftKey\n      || [\"Tab\", \"CapsLock\", \"Esc\", \"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(\n        e.code || e.key || this.keyCodeToKey(e?.keyCode)\n      )\n    )\n  }\n}\n\nexport default PhysicalKeyboard;\n", "import \"./css/CandidateBox.css\";\n\nimport Utilities from \"../services/Utilities\";\nimport {\n  CandidateBoxParams,\n  CandidateBoxRenderParams,\n  CandidateBoxShowParams,\n  KeyboardOptions,\n} from \"./../interfaces\";\n\nclass CandidateBox {\n  utilities: Utilities;\n  options: KeyboardOptions;\n  candidateBoxElement!: HTMLDivElement;\n  pageIndex = 0;\n  pageSize: number;\n\n  constructor({ utilities, options }: CandidateBoxParams) {\n    this.utilities = utilities;\n    this.options = options;\n    Utilities.bindMethods(CandidateBox, this);\n    this.pageSize = this.utilities.getOptions().layoutCandidatesPageSize || 5;\n  }\n\n  destroy() {\n    if (this.candidateBoxElement) {\n      this.candidateBoxElement.remove();\n      this.pageIndex = 0;\n    }\n  }\n\n  show({\n    candidateValue,\n    targetElement,\n    onSelect,\n  }: CandidateBoxShowParams): void {\n    if (!candidateValue || !candidateValue.length) {\n      return;\n    }\n\n    const candidateListPages = this.utilities.chunkArray(\n      candidateValue.split(\" \"),\n      this.pageSize\n    );\n\n    this.renderPage({\n      candidateListPages,\n      targetElement,\n      pageIndex: this.pageIndex,\n      nbPages: candidateListPages.length,\n      onItemSelected: (selectedCandidate: string, e: MouseEvent) => {\n        onSelect(selectedCandidate, e);\n        this.destroy();\n      },\n    });\n  }\n\n  renderPage({\n    candidateListPages,\n    targetElement,\n    pageIndex,\n    nbPages,\n    onItemSelected,\n  }: CandidateBoxRenderParams) {\n    // Remove current candidate box, if any\n    this.candidateBoxElement?.remove();\n\n    // Create candidate box element\n    this.candidateBoxElement = document.createElement(\"div\");\n    this.candidateBoxElement.className = \"hg-candidate-box\";\n\n    // Candidate box list\n    const candidateListULElement = document.createElement(\"ul\");\n    candidateListULElement.className = \"hg-candidate-box-list\";\n\n    // Create Candidate box list items\n    candidateListPages[pageIndex].forEach((candidateListItem) => {\n      const candidateListLIElement = document.createElement(\"li\");\n      const getMouseEvent = () => {\n        const mouseEvent = new (this.options.useTouchEvents ? TouchEvent : MouseEvent)(\"click\");\n        Object.defineProperty(mouseEvent, \"target\", {\n          value: candidateListLIElement,\n        });\n        return mouseEvent;\n      };\n\n      candidateListLIElement.className = \"hg-candidate-box-list-item\";\n      candidateListLIElement.innerHTML = this.options.display?.[candidateListItem] || candidateListItem;\n\n      if(this.options.useTouchEvents) {\n        candidateListLIElement.ontouchstart = (e: any) =>\n          onItemSelected(candidateListItem, e || getMouseEvent());\n      } else {\n        candidateListLIElement.onclick = (e = getMouseEvent() as MouseEvent) =>\n          onItemSelected(candidateListItem, e);\n      }\n\n      // Append list item to ul\n      candidateListULElement.appendChild(candidateListLIElement);\n    });\n\n    // Add previous button\n    const isPrevBtnElementActive = pageIndex > 0;\n    const prevBtnElement = document.createElement(\"div\");\n    prevBtnElement.classList.add(\"hg-candidate-box-prev\");\n    isPrevBtnElementActive &&\n      prevBtnElement.classList.add(\"hg-candidate-box-btn-active\");\n\n    const prevBtnElementClickAction = () => {\n      if (!isPrevBtnElementActive) return;\n      this.renderPage({\n        candidateListPages,\n        targetElement,\n        pageIndex: pageIndex - 1,\n        nbPages,\n        onItemSelected,\n      });\n    };\n\n    if(this.options.useTouchEvents) {\n      prevBtnElement.ontouchstart = prevBtnElementClickAction;\n    } else {\n      prevBtnElement.onclick = prevBtnElementClickAction;\n    }\n    \n    this.candidateBoxElement.appendChild(prevBtnElement);\n\n    // Add elements to container\n    this.candidateBoxElement.appendChild(candidateListULElement);\n\n    // Add next button\n    const isNextBtnElementActive = pageIndex < nbPages - 1;\n    const nextBtnElement = document.createElement(\"div\");\n    nextBtnElement.classList.add(\"hg-candidate-box-next\");\n    isNextBtnElementActive &&\n      nextBtnElement.classList.add(\"hg-candidate-box-btn-active\");\n\n    const nextBtnElementClickAction = () => {\n      if (!isNextBtnElementActive) return;\n      this.renderPage({\n        candidateListPages,\n        targetElement,\n        pageIndex: pageIndex + 1,\n        nbPages,\n        onItemSelected,\n      });\n    };\n\n    if(this.options.useTouchEvents) {\n      nextBtnElement.ontouchstart = nextBtnElementClickAction;\n    } else {\n      nextBtnElement.onclick = nextBtnElementClickAction;\n    }\n\n    this.candidateBoxElement.appendChild(nextBtnElement);\n\n    // Append candidate box to target element\n    targetElement.prepend(this.candidateBoxElement);\n  }\n}\n\nexport default CandidateBox;\n", "import \"./css/Keyboard.css\";\n\nimport { getDefaultLayout } from \"../services/KeyboardLayout\";\nimport PhysicalKeyboard from \"../services/PhysicalKeyboard\";\nimport Utilities from \"../services/Utilities\";\nimport {\n  KeyboardOptions,\n  KeyboardInput,\n  KeyboardButtonElements,\n  KeyboardHandlerEvent,\n  KeyboardElement,\n  SKWindow,\n} from \"../interfaces\";\nimport CandidateBox from \"./CandidateBox\";\n\n/**\n * Root class for simple-keyboard.\n * This class:\n * - Parses the options\n * - Renders the rows and buttons\n * - Handles button functionality\n */\nclass SimpleKeyboard {\n  input!: KeyboardInput;\n  options!: KeyboardOptions;\n  utilities!: Utilities;\n  caretPosition!: number | null;\n  caretPositionEnd!: number | null;\n  keyboardDOM!: KeyboardElement;\n  keyboardPluginClasses!: string;\n  keyboardDOMClass!: string;\n  buttonElements!: KeyboardButtonElements;\n  currentInstanceName!: string;\n  allKeyboardInstances!: { [key: string]: SimpleKeyboard };\n  keyboardInstanceNames!: string[];\n  isFirstKeyboardInstance!: boolean;\n  physicalKeyboard!: PhysicalKeyboard;\n  modules!: { [key: string]: any };\n  activeButtonClass!: string;\n  holdInteractionTimeout!: number;\n  holdTimeout!: number;\n  isMouseHold!: boolean;\n  initialized!: boolean;\n  candidateBox!: CandidateBox | null;\n  keyboardRowsDOM!: KeyboardElement;\n  defaultName = \"default\";\n  activeInputElement: HTMLInputElement | HTMLTextAreaElement | null = null;\n\n  /**\n   * Creates an instance of SimpleKeyboard\n   * @param {Array} selectorOrOptions If first parameter is a string, it is considered the container class. The second parameter is then considered the options object. If first parameter is an object, it is considered the options object.\n   */\n  constructor(\n    selectorOrOptions?: string | HTMLDivElement | KeyboardOptions,\n    keyboardOptions?: KeyboardOptions\n  ) {\n    if (typeof window === \"undefined\") return;\n\n    const {\n      keyboardDOMClass,\n      keyboardDOM,\n      options = {},\n    } = this.handleParams(selectorOrOptions, keyboardOptions);\n\n    /**\n     * Initializing Utilities\n     */\n    this.utilities = new Utilities({\n      getOptions: this.getOptions,\n      getCaretPosition: this.getCaretPosition,\n      getCaretPositionEnd: this.getCaretPositionEnd,\n      dispatch: this.dispatch,\n    });\n\n    /**\n     * Caret position\n     */\n    this.caretPosition = null;\n\n    /**\n     * Caret position end\n     */\n    this.caretPositionEnd = null;\n\n    /**\n     * Processing options\n     */\n    this.keyboardDOM = keyboardDOM;\n\n    /**\n     * @type {object}\n     * @property {object} layout Modify the keyboard layout.\n     * @property {string} layoutName Specifies which layout should be used.\n     * @property {object} display Replaces variable buttons (such as {bksp}) with a human-friendly name (e.g.: “backspace”).\n     * @property {boolean} mergeDisplay By default, when you set the display property, you replace the default one. This setting merges them instead.\n     * @property {string} theme A prop to add your own css classes to the keyboard wrapper. You can add multiple classes separated by a space.\n     * @property {array} buttonTheme A prop to add your own css classes to one or several buttons.\n     * @property {array} buttonAttributes A prop to add your own attributes to one or several buttons.\n     * @property {boolean} debug Runs a console.log every time a key is pressed. Displays the buttons pressed and the current input.\n     * @property {boolean} newLineOnEnter Specifies whether clicking the “ENTER” button will input a newline (\\n) or not.\n     * @property {boolean} tabCharOnTab Specifies whether clicking the “TAB” button will input a tab character (\\t) or not.\n     * @property {string} inputName Allows you to use a single simple-keyboard instance for several inputs.\n     * @property {number} maxLength Restrains all of simple-keyboard inputs to a certain length. This should be used in addition to the input element’s maxlengthattribute.\n     * @property {object} maxLength Restrains simple-keyboard’s individual inputs to a certain length. This should be used in addition to the input element’s maxlengthattribute.\n     * @property {boolean} syncInstanceInputs When set to true, this option synchronizes the internal input of every simple-keyboard instance.\n     * @property {boolean} physicalKeyboardHighlight Enable highlighting of keys pressed on physical keyboard.\n     * @property {boolean} physicalKeyboardHighlightPress Presses keys highlighted by physicalKeyboardHighlight\n     * @property {string} physicalKeyboardHighlightTextColor Define the text color that the physical keyboard highlighted key should have.\n     * @property {string} physicalKeyboardHighlightBgColor Define the background color that the physical keyboard highlighted key should have.\n     * @property {boolean} physicalKeyboardHighlightPressUseClick Whether physicalKeyboardHighlightPress should use clicks to trigger buttons.\n     * @property {boolean} physicalKeyboardHighlightPressUsePointerEvents Whether physicalKeyboardHighlightPress should use pointer events to trigger buttons.\n     * @property {boolean} physicalKeyboardHighlightPreventDefault Whether physicalKeyboardHighlight should use preventDefault to disable default browser actions.\n     * @property {boolean} preventMouseDownDefault Calling preventDefault for the mousedown events keeps the focus on the input.\n     * @property {boolean} preventMouseUpDefault Calling preventDefault for the mouseup events.\n     * @property {boolean} stopMouseDownPropagation Stops pointer down events on simple-keyboard buttons from bubbling to parent elements.\n     * @property {boolean} stopMouseUpPropagation Stops pointer up events on simple-keyboard buttons from bubbling to parent elements.\n     * @property {function(button: string):string} onKeyPress Executes the callback function on key press. Returns button layout name (i.e.: “{shift}”).\n     * @property {function(input: string):string} onChange Executes the callback function on input change. Returns the current input’s string.\n     * @property {function} onRender Executes the callback function every time simple-keyboard is rendered (e.g: when you change layouts).\n     * @property {function} onInit Executes the callback function once simple-keyboard is rendered for the first time (on initialization).\n     * @property {function(keyboard: Keyboard):void} beforeInputUpdate Perform an action before any input change\n     * @property {function(inputs: object):object} onChangeAll Executes the callback function on input change. Returns the input object with all defined inputs.\n     * @property {boolean} useButtonTag Render buttons as a button element instead of a div element.\n     * @property {boolean} disableCaretPositioning A prop to ensure characters are always be added/removed at the end of the string.\n     * @property {object} inputPattern Restrains input(s) change to the defined regular expression pattern.\n     * @property {boolean} useTouchEvents Instructs simple-keyboard to use touch events instead of click events.\n     * @property {boolean} autoUseTouchEvents Enable useTouchEvents automatically when touch device is detected.\n     * @property {boolean} useMouseEvents Opt out of PointerEvents handling, falling back to the prior mouse event logic.\n     * @property {function} destroy Clears keyboard listeners and DOM elements.\n     * @property {boolean} disableButtonHold Disable button hold action.\n     * @property {boolean} rtl Adds unicode right-to-left control characters to input return values.\n     * @property {function} onKeyReleased Executes the callback function on key release.\n     * @property {array} modules Module classes to be loaded by simple-keyboard.\n     * @property {boolean} enableLayoutCandidates Enable input method editor candidate list support.\n     * @property {object} excludeFromLayout Buttons to exclude from layout\n     * @property {number} layoutCandidatesPageSize Determines size of layout candidate list\n     * @property {boolean} layoutCandidatesCaseSensitiveMatch Determines whether layout candidate match should be case sensitive.\n     * @property {boolean} disableCandidateNormalization Disables the automatic normalization for selected layout candidates\n     * @property {boolean} enableLayoutCandidatesKeyPress Enables onKeyPress triggering for layoutCandidate items\n     * @property {boolean} updateCaretOnSelectionChange Updates caret when selectionchange event is fired\n     * @property {boolean} clickOnMouseDown When useMouseEvents is enabled, this option allows you to trigger a button click event on mousedown\n     */\n    this.options = {\n      layoutName: \"default\",\n      theme: \"hg-theme-default\",\n      inputName: \"default\",\n      preventMouseDownDefault: false,\n      enableLayoutCandidates: true,\n      excludeFromLayout: {},\n      ...options,\n    };\n\n    /**\n     * @type {object} Classes identifying loaded plugins\n     */\n    this.keyboardPluginClasses = \"\";\n\n    /**\n     * Bindings\n     */\n    Utilities.bindMethods(SimpleKeyboard, this);\n\n    /**\n     * simple-keyboard uses a non-persistent internal input to keep track of the entered string (the variable `keyboard.input`).\n     * This removes any dependency to input DOM elements. You can type and directly display the value in a div element, for example.\n     * @example\n     * // To get entered input\n     * const input = keyboard.getInput();\n     *\n     * // To clear entered input.\n     * keyboard.clearInput();\n     *\n     * @type {object}\n     * @property {object} default Default SimpleKeyboard internal input.\n     * @property {object} myInputName Example input that can be set through `options.inputName:\"myInputName\"`.\n     */\n    const { inputName = this.defaultName } = this.options;\n    this.input = {};\n    this.input[inputName] = \"\";\n\n    /**\n     * @type {string} DOM class of the keyboard wrapper, normally \"simple-keyboard\" by default.\n     */\n    this.keyboardDOMClass = keyboardDOMClass;\n\n    /**\n     * @type {object} Contains the DOM elements of every rendered button, the key being the button's layout name (e.g.: \"{enter}\").\n     */\n    this.buttonElements = {};\n\n    /**\n     * Simple-keyboard Instances\n     * This enables multiple simple-keyboard support with easier management\n     */\n    if (!(window as SKWindow)[\"SimpleKeyboardInstances\"])\n      (window as SKWindow)[\"SimpleKeyboardInstances\"] = {};\n\n    this.currentInstanceName = this.utilities.camelCase(this.keyboardDOMClass);\n    (window as SKWindow)[\"SimpleKeyboardInstances\"][this.currentInstanceName] = this;\n\n    /**\n     * Instance vars\n     */\n    this.allKeyboardInstances = (window as SKWindow)[\"SimpleKeyboardInstances\"];\n    this.keyboardInstanceNames = Object.keys((window as SKWindow)[\"SimpleKeyboardInstances\"]);\n    this.isFirstKeyboardInstance =\n      this.keyboardInstanceNames[0] === this.currentInstanceName;\n\n    /**\n     * Physical Keyboard support\n     */\n    this.physicalKeyboard = new PhysicalKeyboard({\n      dispatch: this.dispatch,\n      getOptions: this.getOptions,\n    });\n\n    /**\n     * Initializing CandidateBox\n     */\n    this.candidateBox = this.options.enableLayoutCandidates\n      ? new CandidateBox({\n          utilities: this.utilities,\n          options: this.options,\n        })\n      : null;\n\n    /**\n     * Rendering keyboard\n     */\n    if (this.keyboardDOM) this.render();\n    else {\n      console.warn(`\".${keyboardDOMClass}\" was not found in the DOM.`);\n      throw new Error(\"KEYBOARD_DOM_ERROR\");\n    }\n\n    /**\n     * Modules\n     */\n    this.modules = {};\n    this.loadModules();\n  }\n\n  /**\n   * parseParams\n   */\n  handleParams = (\n    selectorOrOptions?: string | HTMLDivElement | KeyboardOptions,\n    keyboardOptions?: KeyboardOptions\n  ): {\n    keyboardDOMClass: string;\n    keyboardDOM: KeyboardElement;\n    options: Partial<KeyboardOptions | undefined>;\n  } => {\n    let keyboardDOMClass;\n    let keyboardDOM;\n    let options;\n\n    /**\n     * If first parameter is a string:\n     * Consider it as an element's class\n     */\n    if (typeof selectorOrOptions === \"string\") {\n      keyboardDOMClass = selectorOrOptions.split(\".\").join(\"\");\n      keyboardDOM = document.querySelector(\n        `.${keyboardDOMClass}`\n      ) as KeyboardElement;\n      options = keyboardOptions;\n\n      /**\n       * If first parameter is an KeyboardElement\n       * Consider it as the keyboard DOM element\n       */\n    } else if (selectorOrOptions instanceof HTMLDivElement) {\n      /**\n       * This element must have a class, otherwise throw\n       */\n      if (!selectorOrOptions.className) {\n        console.warn(\"Any DOM element passed as parameter must have a class.\");\n        throw new Error(\"KEYBOARD_DOM_CLASS_ERROR\");\n      }\n\n      keyboardDOMClass = selectorOrOptions.className.split(\" \")[0];\n      keyboardDOM = selectorOrOptions;\n      options = keyboardOptions;\n\n      /**\n       * Otherwise, search for .simple-keyboard DOM element\n       */\n    } else {\n      keyboardDOMClass = \"simple-keyboard\";\n      keyboardDOM = document.querySelector(\n        `.${keyboardDOMClass}`\n      ) as KeyboardElement;\n      options = selectorOrOptions;\n    }\n\n    return {\n      keyboardDOMClass,\n      keyboardDOM,\n      options,\n    };\n  };\n\n  /**\n   * Getters\n   */\n  getOptions = (): KeyboardOptions => this.options;\n  getCaretPosition = (): number | null => this.caretPosition;\n  getCaretPositionEnd = (): number | null => this.caretPositionEnd;\n\n  /**\n   * Changes the internal caret position\n   * @param {number} position The caret's start position\n   * @param {number} positionEnd The caret's end position\n   */\n  setCaretPosition(position: number | null, endPosition = position): void {\n    this.caretPosition = position;\n    this.caretPositionEnd = endPosition;\n  }\n\n  /**\n   * Retrieve the candidates for a given input\n   * @param input The input string to check\n   */\n  getInputCandidates(\n    input: string\n  ): { candidateKey: string; candidateValue: string } | Record<string, never> {\n    const {\n      layoutCandidates: layoutCandidatesObj,\n      layoutCandidatesCaseSensitiveMatch,\n    } = this.options;\n\n    if (!layoutCandidatesObj || typeof layoutCandidatesObj !== \"object\") {\n      return {};\n    }\n\n    const layoutCandidates = Object.keys(layoutCandidatesObj).filter(\n      (layoutCandidate: string) => {\n        const inputSubstr =\n          input.substring(0, this.getCaretPositionEnd() || 0) || input;\n        const regexp = new RegExp(\n          `${this.utilities.escapeRegex(layoutCandidate)}$`,\n          layoutCandidatesCaseSensitiveMatch ? \"g\" : \"gi\"\n        );\n        const matches = [...inputSubstr.matchAll(regexp)];\n        return !!matches.length;\n      }\n    );\n\n    if (layoutCandidates.length > 1) {\n      const candidateKey = layoutCandidates.sort(\n        (a, b) => b.length - a.length\n      )[0];\n      return {\n        candidateKey,\n        candidateValue: layoutCandidatesObj[candidateKey],\n      };\n    } else if (layoutCandidates.length) {\n      const candidateKey = layoutCandidates[0];\n      return {\n        candidateKey,\n        candidateValue: layoutCandidatesObj[candidateKey],\n      };\n    } else {\n      return {};\n    }\n  }\n\n  /**\n   * Shows a suggestion box with a list of candidate words\n   * @param candidates The chosen candidates string as defined in the layoutCandidates option\n   * @param targetElement The element next to which the candidates box will be shown\n   */\n  showCandidatesBox(\n    candidateKey: string,\n    candidateValue: string,\n    targetElement: KeyboardElement\n  ): void {\n    if (this.candidateBox) {\n      this.candidateBox.show({\n        candidateValue,\n        targetElement,\n        onSelect: (selectedCandidate: string, e: MouseEvent) => {\n          const {\n            layoutCandidatesCaseSensitiveMatch,\n            disableCandidateNormalization,\n            enableLayoutCandidatesKeyPress\n          } = this.options;\n\n          let candidateStr = selectedCandidate;\n\n          if(!disableCandidateNormalization) {\n            /**\n             * Making sure that our suggestions are not composed characters\n             */\n            candidateStr = selectedCandidate.normalize(\"NFD\");\n          }\n\n          /**\n           * Perform an action before any input change\n           */\n          if (typeof this.options.beforeInputUpdate === \"function\") {\n            this.options.beforeInputUpdate(this);\n          }\n\n          const currentInput = this.getInput(this.options.inputName, true);\n          const initialCaretPosition = this.getCaretPositionEnd() || 0;\n          const inputSubstr =\n            currentInput.substring(0, initialCaretPosition || 0) ||\n            currentInput;\n\n          const regexp = new RegExp(\n            `${this.utilities.escapeRegex(candidateKey)}$`,\n            layoutCandidatesCaseSensitiveMatch ? \"g\" : \"gi\"\n          );\n          const newInputSubstr = inputSubstr.replace(\n            regexp,\n            candidateStr\n          );\n          const newInput = currentInput.replace(inputSubstr, newInputSubstr);\n\n          const caretPositionDiff = newInputSubstr.length - inputSubstr.length;\n          let newCaretPosition =\n            (initialCaretPosition || currentInput.length) + caretPositionDiff;\n\n          if (newCaretPosition < 0) newCaretPosition = 0;\n\n          this.setInput(newInput, this.options.inputName, true);\n          this.setCaretPosition(newCaretPosition);\n\n          /**\n           * Calling onKeyPress\n           * We pass in the composed candidate instead of the decomposed one\n           * To prevent confusion for users\n           */\n          if (enableLayoutCandidatesKeyPress && typeof this.options.onKeyPress === \"function\")\n            this.options.onKeyPress(selectedCandidate, e);\n\n          if (typeof this.options.onChange === \"function\")\n            this.options.onChange(\n              this.getInput(this.options.inputName, true),\n              e\n            );\n\n          /**\n           * Calling onChangeAll\n           */\n          if (typeof this.options.onChangeAll === \"function\")\n            this.options.onChangeAll(this.getAllInputs(), e);\n        },\n      });\n    }\n  }\n\n  /**\n   * Handles clicks made to keyboard buttons\n   * @param  {string} button The button's layout name.\n   */\n  handleButtonClicked(button: string, e?: KeyboardHandlerEvent): void {\n    const { inputName = this.defaultName, debug } = this.options;\n    /**\n     * Ignoring placeholder buttons\n     */\n    if (button === \"{//}\") return;\n\n    /**\n     * Creating inputName if it doesn't exist\n     */\n    if (!this.input[inputName]) this.input[inputName] = \"\";\n\n    /**\n     * Perform an action before any input change\n     */\n    if (typeof this.options.beforeInputUpdate === \"function\") {\n      this.options.beforeInputUpdate(this);\n    }\n\n    /**\n     * Calculating new input\n     */\n    const updatedInput = this.utilities.getUpdatedInput(\n      button,\n      this.input[inputName],\n      this.caretPosition,\n      this.caretPositionEnd\n    );\n\n    /**\n     * EDGE CASE: Check for whole input selection changes that will yield same updatedInput\n     */\n    if (this.utilities.isStandardButton(button) && this.activeInputElement) {\n      const isEntireInputSelection =\n        this.input[inputName] &&\n        this.input[inputName] === updatedInput &&\n        this.caretPosition === 0 &&\n        this.caretPositionEnd === updatedInput.length;\n\n      if (isEntireInputSelection) {\n        this.setInput(\"\", this.options.inputName, true);\n        this.setCaretPosition(0);\n        this.activeInputElement.value = \"\";\n        this.activeInputElement.setSelectionRange(0, 0);\n        this.handleButtonClicked(button, e);\n        return;\n      }\n    }\n\n    /**\n     * Calling onKeyPress\n     */\n    if (typeof this.options.onKeyPress === \"function\")\n      this.options.onKeyPress(button, e);\n\n    if (\n      // If input will change as a result of this button press\n      this.input[inputName] !== updatedInput &&\n      // This pertains to the \"inputPattern\" option:\n      // If inputPattern isn't set\n      (!this.options.inputPattern ||\n        // Or, if it is set and if the pattern is valid - we proceed.\n        (this.options.inputPattern && this.inputPatternIsValid(updatedInput)))\n    ) {\n      /**\n       * If maxLength and handleMaxLength yield true, halting\n       */\n      if (\n        this.options.maxLength &&\n        this.utilities.handleMaxLength(this.input, updatedInput)\n      ) {\n        return;\n      }\n\n      /**\n       * Updating input\n       */\n      const newInputValue = this.utilities.getUpdatedInput(\n        button,\n        this.input[inputName],\n        this.caretPosition,\n        this.caretPositionEnd,\n        true\n      );\n\n      this.setInput(newInputValue, this.options.inputName, true);\n\n      if (debug) console.log(\"Input changed:\", this.getAllInputs());\n\n      if (this.options.debug) {\n        console.log(\n          \"Caret at: \",\n          this.getCaretPosition(),\n          this.getCaretPositionEnd(),\n          `(${this.keyboardDOMClass})`,\n          e?.type\n        );\n      }\n\n      /**\n       * Enforce syncInstanceInputs, if set\n       */\n      if (this.options.syncInstanceInputs) this.syncInstanceInputs();\n\n      /**\n       * Calling onChange\n       */\n      if (typeof this.options.onChange === \"function\")\n        this.options.onChange(this.getInput(this.options.inputName, true), e);\n\n      /**\n       * Calling onChangeAll\n       */\n      if (typeof this.options.onChangeAll === \"function\")\n        this.options.onChangeAll(this.getAllInputs(), e);\n\n      /**\n       * Check if this new input has candidates (suggested words)\n       */\n      if (e?.target && this.options.enableLayoutCandidates) {\n        const { candidateKey, candidateValue } =\n          this.getInputCandidates(updatedInput);\n\n        if (candidateKey && candidateValue) {\n          this.showCandidatesBox(\n            candidateKey,\n            candidateValue,\n            this.keyboardDOM\n          );\n        } else {\n          this.candidateBox?.destroy();\n        }\n      }\n    }\n\n    /**\n     * After a button is clicked the selection (if any) will disappear\n     * we should reflect this in our state, as applicable\n     */\n    if(this.caretPositionEnd && this.caretPosition !== this.caretPositionEnd){\n      this.setCaretPosition(this.caretPositionEnd, this.caretPositionEnd);\n\n      if(this.activeInputElement){\n        this.activeInputElement.setSelectionRange(this.caretPositionEnd, this.caretPositionEnd);\n      }\n      \n      if(this.options.debug){\n        console.log(\"Caret position aligned\", this.caretPosition);\n      }\n    }\n\n    if (debug) {\n      console.log(\"Key pressed:\", button);\n    }\n  }\n\n  /**\n   * Get mouse hold state\n   */\n  getMouseHold() {\n    return this.isMouseHold;\n  }\n\n  /**\n   * Mark mouse hold state as set\n   */\n  setMouseHold(value: boolean) {\n    if (this.options.syncInstanceInputs) {\n      this.dispatch((instance: SimpleKeyboard) => {\n        instance.isMouseHold = value;\n      });\n    } else {\n      this.isMouseHold = value;\n    }\n  }\n\n  /**\n   * Handles button mousedown\n   */\n  /* istanbul ignore next */\n  handleButtonMouseDown(button: string, e: KeyboardHandlerEvent): void {\n    if (e) {\n      /**\n       * Handle event options\n       */\n      if (this.options.preventMouseDownDefault) e.preventDefault();\n      if (this.options.stopMouseDownPropagation) e.stopPropagation();\n\n      /**\n       * Add active class\n       */\n      e.target.classList.add(this.activeButtonClass);\n    }\n\n    if (this.holdInteractionTimeout) clearTimeout(this.holdInteractionTimeout);\n    if (this.holdTimeout) clearTimeout(this.holdTimeout);\n\n    /**\n     * @type {boolean} Whether the mouse is being held onKeyPress\n     */\n    this.setMouseHold(true);\n\n    /**\n     * @type {object} Time to wait until a key hold is detected\n     */\n    if (!this.options.disableButtonHold) {\n      this.holdTimeout = window.setTimeout(() => {\n        if (\n          (this.getMouseHold() &&\n            // TODO: This needs to be configurable through options\n            ((!button.includes(\"{\") && !button.includes(\"}\")) ||\n              button === \"{delete}\" ||\n              button === \"{backspace}\" ||\n              button === \"{bksp}\" ||\n              button === \"{space}\" ||\n              button === \"{tab}\")) ||\n          button === \"{arrowright}\" ||\n          button === \"{arrowleft}\" ||\n          button === \"{arrowup}\" ||\n          button === \"{arrowdown}\"\n        ) {\n          if (this.options.debug) console.log(\"Button held:\", button);\n\n          this.handleButtonHold(button);\n        }\n        clearTimeout(this.holdTimeout);\n      }, 500);\n    }\n  }\n\n  /**\n   * Handles button mouseup\n   */\n  handleButtonMouseUp(button?: string, e?: KeyboardHandlerEvent): void {\n    if (e) {\n      /**\n       * Handle event options\n       */\n      if (this.options.preventMouseUpDefault && e.preventDefault)\n        e.preventDefault();\n      if (this.options.stopMouseUpPropagation && e.stopPropagation)\n        e.stopPropagation();\n\n      /* istanbul ignore next */\n      const isKeyboard =\n        e.target === this.keyboardDOM ||\n        (e.target && this.keyboardDOM.contains(e.target)) ||\n        (this.candidateBox &&\n          this.candidateBox.candidateBoxElement &&\n          (e.target === this.candidateBox.candidateBoxElement ||\n            (e.target &&\n              this.candidateBox.candidateBoxElement.contains(e.target))));\n\n      /**\n       * On click outside, remove candidateBox\n       */\n      if (!isKeyboard && this.candidateBox) {\n        this.candidateBox.destroy();\n      }\n    }\n\n    /**\n     * Remove active class\n     */\n    this.recurseButtons((buttonElement: Element) => {\n      buttonElement.classList.remove(this.activeButtonClass);\n    });\n\n    this.setMouseHold(false);\n    if (this.holdInteractionTimeout) clearTimeout(this.holdInteractionTimeout);\n\n    /**\n     * Calling onKeyReleased\n     */\n    if (button && typeof this.options.onKeyReleased === \"function\")\n      this.options.onKeyReleased(button, e);\n  }\n\n  /**\n   * Handles container mousedown\n   */\n  handleKeyboardContainerMouseDown(e: KeyboardHandlerEvent): void {\n    /**\n     * Handle event options\n     */\n    if (this.options.preventMouseDownDefault) e.preventDefault();\n  }\n\n  /**\n   * Handles button hold\n   */\n  /* istanbul ignore next */\n  handleButtonHold(button: string): void {\n    if (this.holdInteractionTimeout) clearTimeout(this.holdInteractionTimeout);\n\n    /**\n     * @type {object} Timeout dictating the speed of key hold iterations\n     */\n    this.holdInteractionTimeout = window.setTimeout(() => {\n      if (this.getMouseHold()) {\n        this.handleButtonClicked(button);\n        this.handleButtonHold(button);\n      } else {\n        clearTimeout(this.holdInteractionTimeout);\n      }\n    }, 100);\n  }\n\n  /**\n   * Send a command to all simple-keyboard instances (if you have several instances).\n   */\n  syncInstanceInputs(): void {\n    this.dispatch((instance: SimpleKeyboard) => {\n      instance.replaceInput(this.input);\n      instance.setCaretPosition(this.caretPosition, this.caretPositionEnd);\n    });\n  }\n\n  /**\n   * Clear the keyboard’s input.\n   * @param {string} [inputName] optional - the internal input to select\n   */\n  clearInput(\n    inputName: string = this.options.inputName || this.defaultName\n  ): void {\n    this.input[inputName] = \"\";\n\n    /**\n     * Reset caretPosition\n     */\n    this.setCaretPosition(0);\n\n    /**\n     * Enforce syncInstanceInputs, if set\n     */\n    if (this.options.syncInstanceInputs) this.syncInstanceInputs();\n  }\n\n  /**\n   * Get the keyboard’s input (You can also get it from the onChange prop).\n   * @param  {string} [inputName] optional - the internal input to select\n   */\n  getInput(\n    inputName: string = this.options.inputName || this.defaultName,\n    skipSync = false\n  ): string {\n    /**\n     * Enforce syncInstanceInputs, if set\n     */\n    if (this.options.syncInstanceInputs && !skipSync) this.syncInstanceInputs();\n\n    if (this.options.rtl) {\n      // Remove existing control chars\n      const inputWithoutRTLControl = this.input[inputName]\n        .replace(\"\\u202B\", \"\")\n        .replace(\"\\u202C\", \"\");\n\n      return \"\\u202B\" + inputWithoutRTLControl + \"\\u202C\";\n    } else {\n      return this.input[inputName];\n    }\n  }\n\n  /**\n   * Get all simple-keyboard inputs\n   */\n  getAllInputs(): KeyboardInput {\n    const output = {} as KeyboardInput;\n    const inputNames = Object.keys(this.input);\n\n    inputNames.forEach((inputName) => {\n      output[inputName] = this.getInput(inputName, true);\n    });\n\n    return output;\n  }\n\n  /**\n   * Set the keyboard’s input.\n   * @param  {string} input the input value\n   * @param  {string} inputName optional - the internal input to select\n   */\n  setInput(\n    input: string,\n    inputName: string = this.options.inputName || this.defaultName,\n    skipSync?: boolean\n  ): void {\n    this.input[inputName] = input;\n\n    /**\n     * Enforce syncInstanceInputs, if set\n     */\n    if (!skipSync && this.options.syncInstanceInputs) this.syncInstanceInputs();\n  }\n\n  /**\n   * Replace the input object (`keyboard.input`)\n   * @param  {object} inputObj The input object\n   */\n  replaceInput(inputObj: KeyboardInput): void {\n    this.input = inputObj;\n  }\n\n  /**\n   * Set new option or modify existing ones after initialization.\n   * @param  {object} options The options to set\n   */\n  setOptions(options = {}): void {\n    const changedOptions = this.changedOptions(options);\n    this.options = Object.assign(this.options, options);\n\n    if (changedOptions.length) {\n      if (this.options.debug) {\n        console.log(\"changedOptions\", changedOptions);\n      }\n\n      /**\n       * Some option changes require adjustments before re-render\n       */\n      this.onSetOptions(changedOptions);\n\n      /**\n       * Rendering\n       */\n      this.render();\n    }\n  }\n\n  /**\n   * Detecting changes to non-function options\n   * This allows us to ascertain whether a button re-render is needed\n   */\n  changedOptions(newOptions: Partial<KeyboardOptions>): string[] {\n    return Object.keys(newOptions).filter(\n      (optionName) =>\n        JSON.stringify(newOptions[optionName]) !==\n        JSON.stringify(this.options[optionName])\n    );\n  }\n\n  /**\n   * Executing actions depending on changed options\n   * @param  {object} options The options to set\n   */\n  onSetOptions(changedOptions: string[] = []): void {\n    /**\n     * Changed: layoutName\n     */\n    if (changedOptions.includes(\"layoutName\")) {\n      /**\n       * Reset candidateBox\n       */\n      if (this.candidateBox) {\n        this.candidateBox.destroy();\n      }\n    }\n\n    /**\n     * Changed: layoutCandidatesPageSize, layoutCandidates\n     */\n    if (\n      changedOptions.includes(\"layoutCandidatesPageSize\") ||\n      changedOptions.includes(\"layoutCandidates\")\n    ) {\n      /**\n       * Reset and recreate candidateBox\n       */\n      if (this.candidateBox) {\n        this.candidateBox.destroy();\n        this.candidateBox = new CandidateBox({\n          utilities: this.utilities,\n          options: this.options,\n        });\n      }\n    }\n  }\n\n  /**\n   * Remove all keyboard rows and reset keyboard values.\n   * Used internally between re-renders.\n   */\n  resetRows(): void {\n    if (this.keyboardRowsDOM) {\n      this.keyboardRowsDOM.remove();\n    }\n\n    this.keyboardDOM.className = this.keyboardDOMClass;\n    this.keyboardDOM.setAttribute(\"data-skInstance\", this.currentInstanceName);\n    this.buttonElements = {};\n  }\n\n  /**\n   * Send a command to all simple-keyboard instances at once (if you have multiple instances).\n   * @param  {function(instance: object, key: string)} callback Function to run on every instance\n   */\n  // eslint-disable-next-line no-unused-vars\n  dispatch(callback: (instance: SimpleKeyboard, key?: string) => void): void {\n    if (!(window as SKWindow)[\"SimpleKeyboardInstances\"]) {\n      console.warn(\n        `SimpleKeyboardInstances is not defined. Dispatch cannot be called.`\n      );\n      throw new Error(\"INSTANCES_VAR_ERROR\");\n    }\n\n    return Object.keys((window as SKWindow)[\"SimpleKeyboardInstances\"]).forEach((key) => {\n      callback((window as SKWindow)[\"SimpleKeyboardInstances\"][key], key);\n    });\n  }\n\n  /**\n   * Adds/Modifies an entry to the `buttonTheme`. Basically a way to add a class to a button.\n   * @param  {string} buttons List of buttons to select (separated by a space).\n   * @param  {string} className Classes to give to the selected buttons (separated by space).\n   */\n  addButtonTheme(buttons: string, className: string): void {\n    if (!className || !buttons) return;\n\n    buttons.split(\" \").forEach((button) => {\n      className.split(\" \").forEach((classNameItem) => {\n        if (!this.options.buttonTheme) this.options.buttonTheme = [];\n\n        let classNameFound = false;\n\n        /**\n         * If class is already defined, we add button to class definition\n         */\n        this.options.buttonTheme.map((buttonTheme) => {\n          if (buttonTheme?.class.split(\" \").includes(classNameItem)) {\n            classNameFound = true;\n\n            const buttonThemeArray = buttonTheme.buttons.split(\" \");\n            if (!buttonThemeArray.includes(button)) {\n              classNameFound = true;\n              buttonThemeArray.push(button);\n              buttonTheme.buttons = buttonThemeArray.join(\" \");\n            }\n          }\n          return buttonTheme;\n        });\n\n        /**\n         * If class is not defined, we create a new entry\n         */\n        if (!classNameFound) {\n          this.options.buttonTheme.push({\n            class: classNameItem,\n            buttons: buttons,\n          });\n        }\n      });\n    });\n\n    this.render();\n  }\n\n  /**\n   * Removes/Amends an entry to the `buttonTheme`. Basically a way to remove a class previously added to a button through buttonTheme or addButtonTheme.\n   * @param  {string} buttons List of buttons to select (separated by a space).\n   * @param  {string} className Classes to give to the selected buttons (separated by space).\n   */\n  removeButtonTheme(buttons: string, className: string): void {\n    /**\n     * When called with empty parameters, remove all button themes\n     */\n    if (!buttons && !className) {\n      this.options.buttonTheme = [];\n      this.render();\n      return;\n    }\n\n    /**\n     * If buttons are passed and buttonTheme has items\n     */\n    if (\n      buttons &&\n      Array.isArray(this.options.buttonTheme) &&\n      this.options.buttonTheme.length\n    ) {\n      const buttonArray = buttons.split(\" \");\n      buttonArray.forEach((button) => {\n        this.options?.buttonTheme?.map((buttonTheme, index) => {\n          /**\n           * If className is set, we affect the buttons only for that class\n           * Otherwise, we afect all classes\n           */\n          if (\n            (buttonTheme &&\n              className &&\n              className.includes(buttonTheme.class)) ||\n            !className\n          ) {\n            const filteredButtonArray = buttonTheme?.buttons\n              .split(\" \")\n              .filter((item) => item !== button);\n\n            /**\n             * If buttons left, return them, otherwise, remove button Theme\n             */\n            if (buttonTheme && filteredButtonArray?.length) {\n              buttonTheme.buttons = filteredButtonArray.join(\" \");\n            } else {\n              this.options.buttonTheme?.splice(index, 1);\n              buttonTheme = null;\n            }\n          }\n\n          return buttonTheme;\n        });\n      });\n\n      this.render();\n    }\n  }\n\n  /**\n   * Get the DOM Element of a button. If there are several buttons with the same name, an array of the DOM Elements is returned.\n   * @param  {string} button The button layout name to select\n   */\n  getButtonElement(\n    button: string\n  ): KeyboardElement | KeyboardElement[] | undefined {\n    let output;\n\n    const buttonArr = this.buttonElements[button];\n    if (buttonArr) {\n      if (buttonArr.length > 1) {\n        output = buttonArr;\n      } else {\n        output = buttonArr[0];\n      }\n    }\n\n    return output;\n  }\n\n  /**\n   * This handles the \"inputPattern\" option\n   * by checking if the provided inputPattern passes\n   */\n  inputPatternIsValid(inputVal: string): boolean {\n    const inputPatternRaw = this.options.inputPattern;\n    let inputPattern;\n\n    /**\n     * Check if input pattern is global or targeted to individual inputs\n     */\n    if (inputPatternRaw instanceof RegExp) {\n      inputPattern = inputPatternRaw;\n    } else {\n      inputPattern =\n        inputPatternRaw[this.options.inputName || this.defaultName];\n    }\n\n    if (inputPattern && inputVal) {\n      const didInputMatch = inputPattern.test(inputVal);\n\n      if (this.options.debug) {\n        console.log(\n          `inputPattern (\"${inputPattern}\"): ${\n            didInputMatch ? \"passed\" : \"did not pass!\"\n          }`\n        );\n      }\n\n      return didInputMatch;\n    } else {\n      /**\n       * inputPattern doesn't seem to be set for the current input, or input is empty. Pass.\n       */\n      return true;\n    }\n  }\n\n  /**\n   * Handles simple-keyboard event listeners\n   */\n  setEventListeners(): void {\n    /**\n     * Only first instance should set the event listeners\n     */\n    if (this.isFirstKeyboardInstance || !this.allKeyboardInstances) {\n      if (this.options.debug) {\n        console.log(`Caret handling started (${this.keyboardDOMClass})`);\n      }\n\n      const { physicalKeyboardHighlightPreventDefault = false } = this.options;\n\n      /**\n       * Event Listeners\n       */\n      document.addEventListener(\"keyup\", this.handleKeyUp, physicalKeyboardHighlightPreventDefault);\n      document.addEventListener(\"keydown\", this.handleKeyDown, physicalKeyboardHighlightPreventDefault);\n      document.addEventListener(\"mouseup\", this.handleMouseUp);\n      document.addEventListener(\"touchend\", this.handleTouchEnd);\n\n      if (this.options.updateCaretOnSelectionChange) {\n        document.addEventListener(\"selectionchange\", this.handleSelectionChange);\n      }\n\n      document.addEventListener(\"select\", this.handleSelect);\n    }\n  }\n\n  /**\n   * Event Handler: KeyUp\n   */\n  handleKeyUp(event: KeyboardHandlerEvent): void {\n    this.caretEventHandler(event);\n\n    if (this.options.physicalKeyboardHighlight) {\n      this.physicalKeyboard.handleHighlightKeyUp(event);\n    }\n  }\n\n  /**\n   * Event Handler: KeyDown\n   */\n  handleKeyDown(event: KeyboardHandlerEvent): void {\n    if (this.options.physicalKeyboardHighlight) {\n      this.physicalKeyboard.handleHighlightKeyDown(event);\n    }\n  }\n\n  /**\n   * Event Handler: MouseUp\n   */\n  handleMouseUp(event: KeyboardHandlerEvent): void {\n    this.caretEventHandler(event);\n  }\n\n  /**\n   * Event Handler: TouchEnd\n   */\n  /* istanbul ignore next */\n  handleTouchEnd(event: KeyboardHandlerEvent): void {\n    this.caretEventHandler(event);\n  }\n\n  /**\n   * Event Handler: Select\n   */\n  /* istanbul ignore next */\n  handleSelect(event: KeyboardHandlerEvent): void {\n    this.caretEventHandler(event);\n  }\n\n  /**\n   * Event Handler: SelectionChange\n   */\n  /* istanbul ignore next */\n  handleSelectionChange(event: KeyboardHandlerEvent): void {\n    /**\n     * Firefox is not reporting the correct caret position through this event\n     * https://github.com/hodgef/simple-keyboard/issues/1839\n     */\n    if(navigator.userAgent.includes('Firefox')){\n      return;\n    }\n    this.caretEventHandler(event);\n  }\n\n  /**\n   * Called by {@link setEventListeners} when an event that warrants a cursor position update is triggered\n   */\n  caretEventHandler(event: KeyboardHandlerEvent): void {\n    let targetTagName: string;\n    if (event.target.tagName) {\n      targetTagName = event.target.tagName.toLowerCase();\n    }\n\n    this.dispatch((instance) => {\n      let isKeyboard =\n        event.target === instance.keyboardDOM ||\n        (event.target && instance.keyboardDOM.contains(event.target));\n\n      /**\n       * If syncInstanceInputs option is enabled, make isKeyboard match any instance\n       * not just the current one\n       */\n      if (this.options.syncInstanceInputs && Array.isArray(event.path)) {\n        isKeyboard = event.path.some((item: HTMLElement) =>\n          item?.hasAttribute?.(\"data-skInstance\")\n        );\n      }\n\n      if (\n        (targetTagName === \"textarea\" ||\n          (targetTagName === \"input\" &&\n            [\"text\", \"search\", \"url\", \"tel\", \"password\"].includes(\n              event.target.type\n            ))) &&\n        !instance.options.disableCaretPositioning\n      ) {\n        /**\n         * Tracks current cursor position\n         * As keys are pressed, text will be added/removed at that position within the input.\n         */\n        let selectionStart = event.target.selectionStart;\n        let selectionEnd = event.target.selectionEnd;\n\n        if(instance.options.rtl){\n          selectionStart = instance.utilities.getRtlOffset(selectionStart, instance.getInput());\n          selectionEnd = instance.utilities.getRtlOffset(selectionEnd, instance.getInput());\n        }\n\n        instance.setCaretPosition(selectionStart, selectionEnd);\n\n        /**\n         * Tracking current input in order to handle caret positioning edge cases\n         */\n        instance.activeInputElement = event.target;\n\n        if (instance.options.debug) {\n          console.log(\n            \"Caret at: \",\n            instance.getCaretPosition(),\n            instance.getCaretPositionEnd(),\n            event && event.target.tagName.toLowerCase(),\n            `(${instance.keyboardDOMClass})`,\n            event?.type\n          );\n        }\n      } else if (\n        (instance.options.disableCaretPositioning || !isKeyboard) &&\n        event?.type !== \"selectionchange\"\n      ) {\n        /**\n         * If we toggled off disableCaretPositioning, we must ensure caretPosition doesn't persist once reactivated.\n         */\n        instance.setCaretPosition(null);\n\n        /**\n         * Resetting activeInputElement\n         */\n        instance.activeInputElement = null;\n\n        if (instance.options.debug) {\n          console.log(\n            `Caret position reset due to \"${event?.type}\" event`,\n            event\n          );\n        }\n      }\n    });\n  }\n\n  /**\n   * Execute an operation on each button\n   */\n  recurseButtons(fn: any): void {\n    if (!fn) return;\n\n    Object.keys(this.buttonElements).forEach((buttonName) =>\n      this.buttonElements[buttonName].forEach(fn)\n    );\n  }\n\n  /**\n   * Destroy keyboard listeners and DOM elements\n   */\n  destroy(): void {\n    if (this.options.debug)\n      console.log(\n        `Destroying simple-keyboard instance: ${this.currentInstanceName}`\n      );\n\n    const { physicalKeyboardHighlightPreventDefault = false } = this.options;\n\n    /**\n     * Remove document listeners\n     */\n    document.removeEventListener(\"keyup\", this.handleKeyUp, physicalKeyboardHighlightPreventDefault);\n    document.removeEventListener(\"keydown\", this.handleKeyDown, physicalKeyboardHighlightPreventDefault);\n    document.removeEventListener(\"mouseup\", this.handleMouseUp);\n    document.removeEventListener(\"touchend\", this.handleTouchEnd);\n    document.removeEventListener(\"select\", this.handleSelect);\n\n    // selectionchange is causing caret update issues on Chrome\n    // https://github.com/hodgef/simple-keyboard/issues/2346\n    if (this.options.updateCaretOnSelectionChange) {\n      document.removeEventListener(\"selectionchange\", this.handleSelectionChange);\n    }\n\n    document.onpointerup = null;\n    document.ontouchend = null;\n    document.ontouchcancel = null;\n    document.onmouseup = null;\n\n    /**\n     * Remove buttons\n     */\n    const deleteButton = (buttonElement: KeyboardElement | null) => {\n      if (buttonElement) {\n        buttonElement.onpointerdown = null;\n        buttonElement.onpointerup = null;\n        buttonElement.onpointercancel = null;\n        buttonElement.ontouchstart = null;\n        buttonElement.ontouchend = null;\n        buttonElement.ontouchcancel = null;\n        buttonElement.onclick = null;\n        buttonElement.onmousedown = null;\n        buttonElement.onmouseup = null;\n\n        buttonElement.remove();\n        buttonElement = null;\n      }\n    };\n\n    this.recurseButtons(deleteButton);\n\n    /**\n     * Remove wrapper events\n     */\n    this.keyboardDOM.onpointerdown = null;\n    this.keyboardDOM.ontouchstart = null;\n    this.keyboardDOM.onmousedown = null;\n\n    /**\n     * Clearing keyboard rows\n     */\n    this.resetRows();\n\n    /**\n     * Candidate box\n     */\n    if (this.candidateBox) {\n      this.candidateBox.destroy();\n      this.candidateBox = null;\n    }\n\n    /**\n     * Clearing activeInputElement\n     */\n    this.activeInputElement = null;\n\n    /**\n     * Removing instance attribute\n     */\n    this.keyboardDOM.removeAttribute(\"data-skInstance\");\n\n    /**\n     * Clearing keyboardDOM\n     */\n    this.keyboardDOM.innerHTML = \"\";\n\n    /**\n     * Remove instance\n     */\n    (window as SKWindow)[\"SimpleKeyboardInstances\"][this.currentInstanceName] = null;\n    delete (window as SKWindow)[\"SimpleKeyboardInstances\"][this.currentInstanceName];\n\n    /**\n     * Reset initialized flag\n     */\n    this.initialized = false;\n  }\n\n  /**\n   * Process buttonTheme option\n   */\n  getButtonThemeClasses(button: string): string[] {\n    const buttonTheme = this.options.buttonTheme;\n    let buttonClasses: string[] = [];\n\n    if (Array.isArray(buttonTheme)) {\n      buttonTheme.forEach((themeObj) => {\n        if (\n          themeObj &&\n          themeObj.class &&\n          typeof themeObj.class === \"string\" &&\n          themeObj.buttons &&\n          typeof themeObj.buttons === \"string\"\n        ) {\n          const themeObjClasses = themeObj.class.split(\" \");\n          const themeObjButtons = themeObj.buttons.split(\" \");\n\n          if (themeObjButtons.includes(button)) {\n            buttonClasses = [...buttonClasses, ...themeObjClasses];\n          }\n        } else {\n          console.warn(\n            `Incorrect \"buttonTheme\". Please check the documentation.`,\n            themeObj\n          );\n        }\n      });\n    }\n\n    return buttonClasses;\n  }\n\n  /**\n   * Process buttonAttributes option\n   */\n  setDOMButtonAttributes(button: string, callback: any): void {\n    const buttonAttributes = this.options.buttonAttributes;\n\n    if (Array.isArray(buttonAttributes)) {\n      buttonAttributes.forEach((attrObj) => {\n        if (\n          attrObj.attribute &&\n          typeof attrObj.attribute === \"string\" &&\n          attrObj.value &&\n          typeof attrObj.value === \"string\" &&\n          attrObj.buttons &&\n          typeof attrObj.buttons === \"string\"\n        ) {\n          const attrObjButtons = attrObj.buttons.split(\" \");\n\n          if (attrObjButtons.includes(button)) {\n            callback(attrObj.attribute, attrObj.value);\n          }\n        } else {\n          console.warn(\n            `Incorrect \"buttonAttributes\". Please check the documentation.`,\n            attrObj\n          );\n        }\n      });\n    }\n  }\n\n  onTouchDeviceDetected() {\n    /**\n     * Processing autoTouchEvents\n     */\n    this.processAutoTouchEvents();\n\n    /**\n     * Disabling contextual window on touch devices\n     */\n    this.disableContextualWindow();\n  }\n\n  /**\n   * Disabling contextual window for hg-button\n   */\n  /* istanbul ignore next */\n  disableContextualWindow() {\n    window.oncontextmenu = (event: KeyboardHandlerEvent) => {\n      if (event.target.classList?.contains(\"hg-button\")) {\n        event.preventDefault();\n        event.stopPropagation();\n        return false;\n      }\n    };\n  }\n\n  /**\n   * Process autoTouchEvents option\n   */\n  processAutoTouchEvents() {\n    if (this.options.autoUseTouchEvents) {\n      this.options.useTouchEvents = true;\n\n      if (this.options.debug) {\n        console.log(\n          `autoUseTouchEvents: Touch device detected, useTouchEvents enabled.`\n        );\n      }\n    }\n  }\n\n  /**\n   * Executes the callback function once simple-keyboard is rendered for the first time (on initialization).\n   */\n  onInit() {\n    if (this.options.debug) {\n      console.log(`${this.keyboardDOMClass} Initialized`);\n    }\n\n    /**\n     * setEventListeners\n     */\n    this.setEventListeners();\n\n    if (typeof this.options.onInit === \"function\") this.options.onInit(this);\n  }\n\n  /**\n   * Executes the callback function before a simple-keyboard render.\n   */\n  beforeFirstRender() {\n    /**\n     * Performing actions when touch device detected\n     */\n    if (this.utilities.isTouchDevice()) {\n      this.onTouchDeviceDetected();\n    }\n\n    if (typeof this.options.beforeFirstRender === \"function\")\n      this.options.beforeFirstRender(this);\n\n    /**\n     * Notify about PointerEvents usage\n     */\n    if (\n      this.isFirstKeyboardInstance &&\n      this.utilities.pointerEventsSupported() &&\n      !this.options.useTouchEvents &&\n      !this.options.useMouseEvents\n    ) {\n      if (this.options.debug) {\n        console.log(\"Using PointerEvents as it is supported by this browser\");\n      }\n    }\n\n    /**\n     * Notify about touch events usage\n     */\n    if (this.options.useTouchEvents) {\n      if (this.options.debug) {\n        console.log(\n          \"useTouchEvents has been enabled. Only touch events will be used.\"\n        );\n      }\n    }\n  }\n\n  /**\n   * Executes the callback function before a simple-keyboard render.\n   */\n  beforeRender() {\n    if (typeof this.options.beforeRender === \"function\")\n      this.options.beforeRender(this);\n  }\n\n  /**\n   * Executes the callback function every time simple-keyboard is rendered (e.g: when you change layouts).\n   */\n  onRender() {\n    if (typeof this.options.onRender === \"function\")\n      this.options.onRender(this);\n  }\n\n  /**\n   * Executes the callback function once all modules have been loaded\n   */\n  onModulesLoaded() {\n    if (typeof this.options.onModulesLoaded === \"function\")\n      this.options.onModulesLoaded(this);\n  }\n\n  /**\n   * Register module\n   */\n  registerModule = (name: string, initCallback: any) => {\n    if (!this.modules[name]) this.modules[name] = {};\n\n    initCallback(this.modules[name]);\n  };\n\n  /**\n   * Load modules\n   */\n  loadModules() {\n    if (Array.isArray(this.options.modules)) {\n      this.options.modules.forEach((KeyboardModule) => {\n        const keyboardModule = this.utilities.isConstructor(KeyboardModule) ?\n          new KeyboardModule(this) : KeyboardModule(this);\n\n        keyboardModule.init && keyboardModule.init(this);\n      });\n\n      this.keyboardPluginClasses = \"modules-loaded\";\n\n      this.render();\n      this.onModulesLoaded();\n    }\n  }\n\n  /**\n   * Get module prop\n   */\n  getModuleProp(name: string, prop: string) {\n    if (!this.modules[name]) return false;\n\n    return this.modules[name][prop];\n  }\n\n  /**\n   * getModulesList\n   */\n  getModulesList() {\n    return Object.keys(this.modules);\n  }\n\n  /**\n   * Parse Row DOM containers\n   */\n  parseRowDOMContainers(\n    rowDOM: HTMLDivElement,\n    rowIndex: number,\n    containerStartIndexes: number[],\n    containerEndIndexes: number[]\n  ) {\n    const rowDOMArray = Array.from(rowDOM.children);\n    let removedElements = 0;\n\n    if (rowDOMArray.length) {\n      containerStartIndexes.forEach((startIndex, arrIndex) => {\n        const endIndex = containerEndIndexes[arrIndex];\n\n        /**\n         * If there exists a respective end index\n         * if end index comes after start index\n         */\n        if (!endIndex || !(endIndex > startIndex)) {\n          return false;\n        }\n\n        /**\n         * Updated startIndex, endIndex\n         * This is since the removal of buttons to place a single button container\n         * results in a modified array size\n         */\n        const updated_startIndex = startIndex - removedElements;\n        const updated_endIndex = endIndex - removedElements;\n\n        /**\n         * Create button container\n         */\n        const containerDOM = document.createElement(\"div\");\n        containerDOM.className += \"hg-button-container\";\n        const containerUID = `${this.options.layoutName}-r${rowIndex}c${arrIndex}`;\n        containerDOM.setAttribute(\"data-skUID\", containerUID);\n\n        /**\n         * Taking elements due to be inserted into container\n         */\n        const containedElements = rowDOMArray.splice(\n          updated_startIndex,\n          updated_endIndex - updated_startIndex + 1\n        );\n        removedElements += updated_endIndex - updated_startIndex;\n\n        /**\n         * Inserting elements to container\n         */\n        containedElements.forEach((element) =>\n          containerDOM.appendChild(element)\n        );\n\n        /**\n         * Adding container at correct position within rowDOMArray\n         */\n        rowDOMArray.splice(updated_startIndex, 0, containerDOM);\n\n        /**\n         * Clearing old rowDOM children structure\n         */\n        rowDOM.innerHTML = \"\";\n\n        /**\n         * Appending rowDOM new children list\n         */\n        rowDOMArray.forEach((element) => rowDOM.appendChild(element));\n\n        if (this.options.debug) {\n          console.log(\n            \"rowDOMContainer\",\n            containedElements,\n            updated_startIndex,\n            updated_endIndex,\n            removedElements + 1\n          );\n        }\n      });\n    }\n\n    return rowDOM;\n  }\n\n  /**\n   * getKeyboardClassString\n   */\n  getKeyboardClassString = (...baseDOMClasses: any[]) => {\n    const keyboardClasses = [this.keyboardDOMClass, ...baseDOMClasses].filter(\n      (DOMClass) => !!DOMClass\n    );\n\n    return keyboardClasses.join(\" \");\n  };\n\n  /**\n   * Renders rows and buttons as per options\n   */\n  render() {\n    /**\n     * Clear keyboard\n     */\n    this.resetRows();\n\n    /**\n     * Calling beforeFirstRender\n     */\n    if (!this.initialized) {\n      this.beforeFirstRender();\n    }\n\n    /**\n     * Calling beforeRender\n     */\n    this.beforeRender();\n\n    const layoutClass = `hg-layout-${this.options.layoutName}`;\n    const layout = this.options.layout || getDefaultLayout();\n    const useTouchEvents = this.options.useTouchEvents || false;\n    const useTouchEventsClass = useTouchEvents ? \"hg-touch-events\" : \"\";\n    const useMouseEvents = this.options.useMouseEvents || false;\n    const disableRowButtonContainers = this.options.disableRowButtonContainers;\n\n    /**\n     * Adding themeClass, layoutClass to keyboardDOM\n     */\n    this.keyboardDOM.className = this.getKeyboardClassString(\n      this.options.theme,\n      layoutClass,\n      this.keyboardPluginClasses,\n      useTouchEventsClass\n    );\n\n    /**\n     * Adding keyboard identifier\n     */\n    this.keyboardDOM.setAttribute(\"data-skInstance\", this.currentInstanceName);\n\n    /**\n     * Create row wrapper\n     */\n    this.keyboardRowsDOM = document.createElement(\"div\");\n    this.keyboardRowsDOM.className = \"hg-rows\";\n\n    /**\n     * Iterating through each row\n     */\n    layout[this.options.layoutName || this.defaultName].forEach(\n      (row: string, rIndex: number) => {\n        let rowArray = row.split(\" \");\n\n        /**\n         * Enforce excludeFromLayout\n         */\n        if (\n          this.options.excludeFromLayout &&\n          this.options.excludeFromLayout[\n            this.options.layoutName || this.defaultName\n          ]\n        ) {\n          rowArray = rowArray.filter(\n            (buttonName) =>\n              this.options.excludeFromLayout &&\n              !this.options.excludeFromLayout[\n                this.options.layoutName || this.defaultName\n              ].includes(buttonName)\n          );\n        }\n\n        /**\n         * Creating empty row\n         */\n        let rowDOM = document.createElement(\"div\");\n        rowDOM.className += \"hg-row\";\n\n        /**\n         * Tracking container indicators in rows\n         */\n        const containerStartIndexes: number[] = [];\n        const containerEndIndexes: number[] = [];\n\n        /**\n         * Iterating through each button in row\n         */\n        rowArray.forEach((button, bIndex) => {\n          /**\n           * Check if button has a container indicator\n           */\n          const buttonHasContainerStart =\n            !disableRowButtonContainers &&\n            typeof button === \"string\" &&\n            button.length > 1 &&\n            button.indexOf(\"[\") === 0;\n\n          const buttonHasContainerEnd =\n            !disableRowButtonContainers &&\n            typeof button === \"string\" &&\n            button.length > 1 &&\n            button.indexOf(\"]\") === button.length - 1;\n\n          /**\n           * Save container start index, if applicable\n           */\n          if (buttonHasContainerStart) {\n            containerStartIndexes.push(bIndex);\n\n            /**\n             * Removing indicator\n             */\n            button = button.replace(/\\[/g, \"\");\n          }\n\n          if (buttonHasContainerEnd) {\n            containerEndIndexes.push(bIndex);\n\n            /**\n             * Removing indicator\n             */\n            button = button.replace(/\\]/g, \"\");\n          }\n\n          /**\n           * Processing button options\n           */\n          const fctBtnClass = this.utilities.getButtonClass(button);\n          const buttonDisplayName = this.utilities.getButtonDisplayName(\n            button,\n            this.options.display,\n            this.options.mergeDisplay\n          );\n\n          /**\n           * Creating button\n           */\n          const buttonType = this.options.useButtonTag ? \"button\" : \"div\";\n          const buttonDOM = document.createElement(buttonType);\n          buttonDOM.className += `hg-button ${fctBtnClass}`;\n\n          /**\n           * Adding buttonTheme\n           */\n          buttonDOM.classList.add(...this.getButtonThemeClasses(button));\n\n          /**\n           * Adding buttonAttributes\n           */\n          this.setDOMButtonAttributes(\n            button,\n            (attribute: string, value: string) => {\n              buttonDOM.setAttribute(attribute, value);\n            }\n          );\n\n          this.activeButtonClass = \"hg-activeButton\";\n\n          /**\n           * Handle button click event\n           */\n          /* istanbul ignore next */\n          if (\n            this.utilities.pointerEventsSupported() &&\n            !useTouchEvents &&\n            !useMouseEvents\n          ) {\n            /**\n             * Handle PointerEvents\n             */\n            buttonDOM.onpointerdown = (e: KeyboardHandlerEvent) => {\n              this.handleButtonClicked(button, e);\n              this.handleButtonMouseDown(button, e);\n            };\n            buttonDOM.onpointerup = (e: KeyboardHandlerEvent) => {\n              this.handleButtonMouseUp(button, e);\n            };\n            buttonDOM.onpointercancel = (e: KeyboardHandlerEvent) => {\n              this.handleButtonMouseUp(button, e);\n            };\n          } else {\n            /**\n             * Fallback for browsers not supporting PointerEvents\n             */\n            if (useTouchEvents) {\n              /**\n               * Handle touch events\n               */\n              buttonDOM.ontouchstart = (e: KeyboardHandlerEvent) => {\n                this.handleButtonClicked(button, e);\n                this.handleButtonMouseDown(button, e);\n              };\n              buttonDOM.ontouchend = (e: KeyboardHandlerEvent) => {\n                this.handleButtonMouseUp(button, e);\n              };\n              buttonDOM.ontouchcancel = (e: KeyboardHandlerEvent) => {\n                this.handleButtonMouseUp(button, e);\n              };\n            } else {\n              /**\n               * Handle mouse events\n               */\n              buttonDOM.onclick = (e: KeyboardHandlerEvent) => {\n                this.setMouseHold(false);\n                /**\n                 * Fire button handler in onclick for compatibility reasons\n                 * This fires handler before onKeyReleased, therefore when that option is set we will fire the handler\n                 * in onmousedown instead\n                 */\n                if (\n                  typeof this.options.onKeyReleased !== \"function\" &&\n                  !(this.options.useMouseEvents && this.options.clickOnMouseDown)\n                ) {\n                  this.handleButtonClicked(button, e);\n                }\n              };\n              buttonDOM.onmousedown = (e: KeyboardHandlerEvent) => {\n                /**\n                 * Fire button handler for onKeyReleased use-case\n                 */\n                if (\n                  (\n                    typeof this.options.onKeyReleased === \"function\" ||\n                    (this.options.useMouseEvents && this.options.clickOnMouseDown)\n                  ) &&\n                  !this.isMouseHold\n                ) {\n                  this.handleButtonClicked(button, e);\n                }\n                this.handleButtonMouseDown(button, e);\n              };\n              buttonDOM.onmouseup = (e: KeyboardHandlerEvent) => {\n                this.handleButtonMouseUp(button, e);\n              };\n            }\n          }\n\n          /**\n           * Adding identifier\n           */\n          buttonDOM.setAttribute(\"data-skBtn\", button);\n\n          /**\n           * Adding unique id\n           * Since there's no limit on spawning same buttons, the unique id ensures you can style every button\n           */\n          const buttonUID = `${this.options.layoutName}-r${rIndex}b${bIndex}`;\n          buttonDOM.setAttribute(\"data-skBtnUID\", buttonUID);\n\n          /**\n           * Adding button label to button\n           */\n          const buttonSpanDOM = document.createElement(\"span\");\n          buttonSpanDOM.innerHTML = buttonDisplayName;\n          buttonDOM.appendChild(buttonSpanDOM);\n\n          /**\n           * Adding to buttonElements\n           */\n          if (!this.buttonElements[button]) this.buttonElements[button] = [];\n\n          this.buttonElements[button].push(buttonDOM);\n\n          /**\n           * Appending button to row\n           */\n          rowDOM.appendChild(buttonDOM);\n        });\n\n        /**\n         * Parse containers in row\n         */\n        rowDOM = this.parseRowDOMContainers(\n          rowDOM,\n          rIndex,\n          containerStartIndexes,\n          containerEndIndexes\n        );\n\n        /**\n         * Appending row to hg-rows\n         */\n        this.keyboardRowsDOM.appendChild(rowDOM);\n      }\n    );\n\n    /**\n     * Appending row to keyboard\n     */\n    this.keyboardDOM.appendChild(this.keyboardRowsDOM);\n\n    /**\n     * Calling onRender\n     */\n    this.onRender();\n\n    if (!this.initialized) {\n      /**\n       * Ensures that onInit and beforeFirstRender are only called once per instantiation\n       */\n      this.initialized = true;\n\n      /**\n       * Handling parent events\n       */\n      /* istanbul ignore next */\n      if (\n        this.utilities.pointerEventsSupported() &&\n        !useTouchEvents &&\n        !useMouseEvents\n      ) {\n        document.onpointerup = (e: KeyboardHandlerEvent) =>\n          this.handleButtonMouseUp(undefined, e);\n        this.keyboardDOM.onpointerdown = (e: KeyboardHandlerEvent) =>\n          this.handleKeyboardContainerMouseDown(e);\n      } else if (useTouchEvents) {\n        /**\n         * Handling ontouchend, ontouchcancel\n         */\n        document.ontouchend = (e: KeyboardHandlerEvent) =>\n          this.handleButtonMouseUp(undefined, e);\n        document.ontouchcancel = (e: KeyboardHandlerEvent) =>\n          this.handleButtonMouseUp(undefined, e);\n\n        this.keyboardDOM.ontouchstart = (e: KeyboardHandlerEvent) =>\n          this.handleKeyboardContainerMouseDown(e);\n      } else if (!useTouchEvents) {\n        /**\n         * Handling mouseup\n         */\n        document.onmouseup = (e: KeyboardHandlerEvent) =>\n          this.handleButtonMouseUp(undefined, e);\n        this.keyboardDOM.onmousedown = (e: KeyboardHandlerEvent) =>\n          this.handleKeyboardContainerMouseDown(e);\n      }\n\n      /**\n       * Calling onInit\n       */\n      this.onInit();\n    }\n  }\n}\n\nexport default SimpleKeyboard;\n", "import { KeyboardLayoutObject } from \"../interfaces\";\n\nexport const getDefaultLayout = (): KeyboardLayoutObject => {\n  return {\n    default: [\n      \"` 1 2 3 4 5 6 7 8 9 0 - = {bksp}\",\n      \"{tab} q w e r t y u i o p [ ] \\\\\",\n      \"{lock} a s d f g h j k l ; ' {enter}\",\n      \"{shift} z x c v b n m , . / {shift}\",\n      \".com @ {space}\",\n    ],\n    shift: [\n      \"~ ! @ # $ % ^ & * ( ) _ + {bksp}\",\n      \"{tab} Q W E R T Y U I O P { } |\",\n      '{lock} A S D F G H J K L : \" {enter}',\n      \"{shift} Z X C V B N M < > ? {shift}\",\n      \".com @ {space}\",\n    ],\n  };\n};\n", "import SimpleKeyboard from \"./components/Keyboard\";\nexport { SimpleKeyboard };\nexport default SimpleKeyboard;\n"], "names": ["Utilities", "_ref", "getOptions", "getCaretPosition", "getCaretPositionEnd", "dispatch", "_classCallCheck", "_defineProperty", "button", "length", "this", "bindMethods", "key", "value", "includes", "buttonTypeClass", "getButtonType", "buttonWithoutBraces", "replace", "buttonNormalized", "concat", "display", "arguments", "undefined", "Object", "assign", "getDefaultDiplay", "input", "caretPos", "caretPosEnd", "moveCaret", "options", "commonParams", "output", "removeAt", "apply", "removeForwardsAt", "addStringAt", "tabCharOnTab", "newLineOnEnter", "Number", "isInteger", "debug", "console", "log", "minus", "newCaretPos", "updateCaretPosAction", "instance", "setCaretPosition", "caretPosition", "source", "str", "position", "positionEnd", "slice", "join", "isMaxLengthReached", "updateCaretPos", "emojiMatchedReg", "substring", "match", "substr", "inputObj", "updatedInput", "max<PERSON><PERSON><PERSON>", "currentInput", "inputName", "condition", "max<PERSON><PERSON><PERSON>Reached", "_typeof", "Boolean", "window", "navigator", "maxTouchPoints", "PointerEvent", "toLowerCase", "trim", "split", "reduce", "word", "toUpperCase", "arr", "size", "_toConsumableArray", "Array", "Math", "ceil", "map", "_", "i", "index", "newIndex", "startMarkerIndex", "indexOf", "f", "Reflect", "construct", "String", "e", "myClass", "_step", "_iterator", "_createForOfIteratorHelper", "getOwnPropertyNames", "prototype", "s", "n", "done", "myMethod", "bind", "err", "PhysicalKeyboard", "_this", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "code", "keyCodeToKey", "keyCode", "physicalKeyboardHighlightPreventDefault", "isModifierKey", "preventDefault", "stopImmediatePropagation", "buttonPressed", "getSimpleKeyboardLayoutKey", "buttonDOM", "buttonName", "standardButtonPressed", "getButtonElement", "functionButtonPressed", "_buttonDOM$", "_buttonDOM$$onpointer", "_buttonDOM", "_buttonDOM$onpointerd", "applyButtonStyle", "buttonElement", "style", "background", "physicalKeyboardHighlightBgColor", "color", "physicalKeyboardHighlightTextColor", "isArray", "for<PERSON>ach", "physicalKeyboardHighlightPress", "physicalKeyboardHighlightPressUsePointerEvents", "onpointerdown", "call", "physicalKeyboardHighlightPressUseClick", "_buttonDOM$2", "click", "handleButtonClicked", "_buttonDOM$3", "_buttonDOM$3$onpointe", "_buttonDOM$onpointeru", "removeAttribute", "onpointerup", "_output", "keyId", "CandidateBox", "utilities", "pageSize", "layoutCandidatesPageSize", "candidateBoxElement", "remove", "pageIndex", "_ref2", "<PERSON><PERSON><PERSON><PERSON>", "targetElement", "onSelect", "candidateListPages", "chunkArray", "renderPage", "nbPages", "onItemSelected", "selectedCandidate", "destroy", "_ref3", "_this$candidateBoxEle", "_this2", "document", "createElement", "className", "candidateListULElement", "candidateListItem", "_this2$options$displa", "candidateListL<PERSON>lement", "getMouseEvent", "mouseEvent", "useTouchEvents", "TouchEvent", "MouseEvent", "defineProperty", "innerHTML", "ontouchstart", "onclick", "append<PERSON><PERSON><PERSON>", "isPrevBtnElementActive", "prevBtnElement", "classList", "add", "prevBtnElementClickAction", "isNextBtnElementActive", "nextBtnElement", "nextBtnElementClickAction", "prepend", "SimpleKeyboard", "selectorOrOptions", "keyboardOptions", "keyboardDOMClass", "keyboardDOM", "querySelector", "HTMLDivElement", "warn", "Error", "caretPositionEnd", "name", "initCallback", "modules", "_len", "baseDOMClasses", "_key", "filter", "DOMClass", "_this$handleParams", "handleParams", "_this$handleParams$op", "_objectSpread", "layoutName", "theme", "preventMouseDownDefault", "enableLayoutCandidates", "excludeFromLayout", "keyboardPluginClasses", "_this$options$inputNa", "defaultName", "buttonElements", "currentInstanceName", "camelCase", "allKeyboardInstances", "keyboardInstanceNames", "keys", "isFirstKeyboardInstance", "physicalKeyboard", "candidate<PERSON><PERSON>", "render", "loadModules", "endPosition", "_this$options", "layoutCandidatesObj", "layoutCandidates", "layoutCandidatesCaseSensitiveMatch", "layoutCandidate", "inputSubstr", "regexp", "RegExp", "escapeRegex", "matchAll", "<PERSON><PERSON><PERSON>", "sort", "a", "b", "_this3", "show", "_this3$options", "disableCandidateNormalization", "enableLayoutCandidatesKeyPress", "candidateStr", "normalize", "beforeInputUpdate", "getInput", "initialCaretPosition", "newInputSubstr", "newInput", "caretPositionDiff", "newCaretPosition", "setInput", "onKeyPress", "onChange", "onChangeAll", "getAllInputs", "_this$options2", "_this$options2$inputN", "getUpdatedInput", "isStandardButton", "activeInputElement", "setSelectionRange", "inputPattern", "inputPatternIsValid", "handleMaxLength", "newInputValue", "type", "syncInstanceInputs", "target", "_this$candidateBox", "_this$getInputCandida", "getInputCandidates", "showCandidatesBox", "isMouseHold", "_this4", "stopMouseDownPropagation", "stopPropagation", "activeButtonClass", "holdInteractionTimeout", "clearTimeout", "holdTimeout", "setMouseHold", "disableButtonHold", "setTimeout", "getMouseHold", "handleButtonHold", "_this5", "preventMouseUpDefault", "stopMouseUpPropagation", "contains", "recurseButtons", "onKeyReleased", "_this6", "_this7", "replaceInput", "skipSync", "rtl", "_this8", "changedOptions", "onSetOptions", "newOptions", "_this9", "optionName", "JSON", "stringify", "keyboardRowsDOM", "setAttribute", "callback", "buttons", "_this0", "classNameItem", "buttonTheme", "classNameFound", "buttonThemeArray", "push", "class", "_this1", "_this1$options", "_buttonTheme", "_this1$options$button", "filteredButtonArray", "item", "splice", "buttonArr", "inputVal", "inputPatternRaw", "didInputMatch", "test", "_this$options$physica", "addEventListener", "handleKeyUp", "handleKeyDown", "handleMouseUp", "handleTouchEnd", "updateCaretOnSelectionChange", "handleSelectionChange", "handleSelect", "event", "caretEventHandler", "physicalKeyboardHighlight", "handleHighlightKeyUp", "handleHighlightKeyDown", "userAgent", "targetTagName", "_this10", "tagName", "isKeyboard", "path", "some", "_item$hasAttribute", "hasAttribute", "disableCaretPositioning", "selectionStart", "selectionEnd", "getRtlOffset", "fn", "_this11", "_this$options$physica2", "removeEventListener", "ontouchend", "ontouchcancel", "onmouseup", "onpointercancel", "onmousedown", "resetRows", "initialized", "buttonClasses", "themeObj", "themeObjClasses", "buttonAttributes", "attrObj", "attribute", "processAutoTouchEvents", "disableContextualWindow", "oncontextmenu", "_event$target$classLi", "autoUseTouchEvents", "setEventListeners", "onInit", "isTouchDevice", "onTouchDeviceDetected", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pointerEventsSupported", "useMouseEvents", "beforeRender", "onRender", "onModulesLoaded", "_this12", "KeyboardModule", "keyboardModule", "isConstructor", "init", "prop", "rowDOM", "rowIndex", "containerStartIndexes", "containerEndIndexes", "_this13", "rowDOMArray", "from", "children", "removedElements", "startIndex", "arrIndex", "endIndex", "updated_startIndex", "updated_endIndex", "containerDOM", "containerUID", "containedElements", "element", "_this14", "layoutClass", "layout", "default", "shift", "useTouchEventsClass", "disableRowButtonContainers", "getKeyboardClassString", "row", "rIndex", "rowArray", "bIndex", "_buttonDOM$classList", "buttonHasContainerStart", "buttonHasContainerEnd", "fctBtnClass", "getButtonClass", "buttonDisplayName", "getButtonDisplayName", "mergeDisplay", "buttonType", "useButtonTag", "getButtonThemeClasses", "setDOMButtonAttributes", "handleButtonMouseDown", "handleButtonMouseUp", "clickOnMouseDown", "buttonUID", "buttonSpanDOM", "parseRowDOMContainers", "handleKeyboardContainerMouseDown"], "sourceRoot": ""}