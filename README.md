<p>
  <a href="https://simple-keyboard.com/demo">
	<img alt="simple-keyboard: Javascript Virtual Keyboard" src="https://user-images.githubusercontent.com/25509135/187999993-fb5422fd-a56f-4a9a-84a9-55c94478f61c.gif">
  </a>
<a href="https://www.npmjs.com/package/simple-keyboard"><img src="https://badgen.net/npm/v/simple-keyboard?color=blue" alt="npm version"></a> <a href="https://github.com/hodgef/simple-keyboard/blob/master/LICENSE"><img src="https://img.shields.io/badge/License-MIT-blue.svg" alt="MIT license"></a> <a href="https://gitlab.com/hodgef/simple-keyboard" target="_blank"><img alt="Mirroring" src="https://img.shields.io/badge/Mirror-GitLab-blue" /></a> <a href="https://github.com/hodgef/simple-keyboard/actions"><img alt="Build Status" src="https://github.com/hodgef/simple-keyboard/workflows/Build/badge.svg" /></a> <a href="https://github.com/hodgef/simple-keyboard/actions"><img alt="Publish Status" src="https://github.com/hodgef/simple-keyboard/workflows/Publish/badge.svg" /></a>
</p>

<blockquote>Virtual Keyboard for Javascript. Compatible with your JS, React, Angular or Vue projects.</blockquote>

## 🚀 Demo

[Demo Showcase (Vanilla, Angular, React, Vue)](https://simple-keyboard.com/demo)

## 📦 Installation & Usage

You can use simple-keyboard as a `<script>` tag from a CDN, or install it from npm.

Check out the [Getting Started](https://simple-keyboard.com/getting-started) docs to begin.

## 📖 Documentation

Check out the [simple-keyboard documentation](https://simple-keyboard.com/documentation) site.

Feel free to browse the [Questions & Answers (FAQ)](https://simple-keyboard.com/qa-use-cases/) page for common use-cases.

### To run demo on your own computer

- Clone this repository
- `npm install`
- `npm start`
- Visit [http://localhost:3000/](http://localhost:3000/)

### Other versions

- [React.js](https://github.com/hodgef/react-simple-keyboard)
- [Angular](https://simple-keyboard.com/demo)
- [Vue.js](https://simple-keyboard.com/demo)

### Questions? Join the chat

<a href="https://discordapp.com/invite/SJexsCG" title="Join our Discord chat" target="_blank"><img src="https://discordapp.com/api/guilds/498978399801573396/widget.png?style=banner2" align="center"></a>

## ✳️ Modules

You can extend simple-keyboard's functionality with [modules](https://hodgef.com/simple-keyboard/modules/). Such as:

- [Autocorrect](https://hodgef.com/simple-keyboard/modules/autocorrect/)
- [Input Mask](https://hodgef.com/simple-keyboard/modules/input-mask/)
- [Key Navigation](https://hodgef.com/simple-keyboard/modules/key-navigation/)
- [Swipe Keyboard](https://hodgef.com/simple-keyboard/modules/swipe-keyboard/)

Want to create your own module? Check out the [Modules page](https://hodgef.com/simple-keyboard/modules/) for instructions.

## 🎯 Compatibility

- Internet Explorer 11
- Edge (Spartan) 16+
- Edge (Anaheim/Edge Chromium) 79+
- Chrome 49+
- Safari 9+
- Firefox 57+
- iOS 9+

> Note: If you don't want to support old browsers, you can use the Modern Browsers bundle ([index.modern.js](https://github.com/hodgef/simple-keyboard/blob/master/build)).

## 🔶 Community Libraries & Integrations
Check out useful community modules for simple-keyboard:<br />
https://github.com/hodgef/simple-keyboard/wiki/Community-Libraries-&-Integrations

## ✅ Contributing

PRs and issues are always welcome. Feel free to submit any issues you have at:
[https://github.com/hodgef/simple-keyboard/issues](https://github.com/hodgef/simple-keyboard/issues)
