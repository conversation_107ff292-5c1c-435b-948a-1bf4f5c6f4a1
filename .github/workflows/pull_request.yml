name: Build PR (Standard)

on: pull_request

jobs:
  build:
    runs-on: ${{ matrix.os }}
    if: ${{ github.actor != 'dependabot[bot]' }}
    strategy:
      matrix:
        node-version: [20.x]
        os: [ubuntu-latest]
    steps:
      - uses: actions/checkout@v1
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node_version }}
      - name: npm install, build, and test
        run: |
          export NODE_OPTIONS=--openssl-legacy-provider
          npm install
          npm run coverage
        env:
          CI: true
