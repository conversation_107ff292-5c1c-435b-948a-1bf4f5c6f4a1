name: Publish

on:
  push:
    branches:
      - master
    paths-ignore:
      - README.md
      - .gitignore
      - .github/**

permissions:
  contents: write
  id-token: write

jobs:
  publish:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        node-version: [20.x]
        os: [ubuntu-latest]
    steps:
      - uses: actions/checkout@v1
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node_version }}
          
      - name: npm install, build, and test
        run: |
          export NODE_OPTIONS=--openssl-legacy-provider
          npm install
          npm run test -- --coverage --watchAll=false

      - name: Setup GIT
        run: |
          git reset --hard
          git config --local --list
          git checkout master
          git config user.email "$GH_EMAIL"
          git config user.name "Francisco <PERSON>"
        env:
          GH_EMAIL: ${{secrets.GH_EMAIL}}

      - name: Bump version
        run: |
          export NODE_OPTIONS=--openssl-legacy-provider
          git reset --hard
          npm version patch
          npm run build
          git add . || true
          git commit -m "Build update" || true
          git push "https://$GITHUB_ACTOR:$<EMAIL>/$GITHUB_REPOSITORY"
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}

      - name: npm publish
        run: |
          export NODE_OPTIONS=--openssl-legacy-provider
          npm config set //registry.npmjs.org/:_authToken=$NODE_AUTH_TOKEN
          npm publish --provenance --access public
        env:
          NODE_AUTH_TOKEN: ${{secrets.NODE_AUTH_TOKEN}}
