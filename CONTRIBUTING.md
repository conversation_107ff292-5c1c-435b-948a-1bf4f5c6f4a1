# Contributing

When contributing to this repository, please first discuss the change you wish to make via issue,
email, or any other method with the owners of this repository before working on a change. 

Please note we have a code of conduct, please follow it in all your interactions with the project.

## Pull Request Guidelines

1. Use PRs to fix issues. New features are discouraged as they bring more maintenance burden over time.
2. Discuss the change you wish to make with the owners of this repository **before** raising a PR. If you do not discuss it beforehand, your PR might end up being rejected and your work will be lost.
3. Please ensure your proposal will not significantly change current functionality or bring along breaking changes.
4. PRs only consisting of typo fixes (or other automated contributions), will not be accepted.
5. Do not add any dependencies, libraries or external codes to the project. All the code submitted should be authored by you.
6. Avoid refactors. The changes should be succint and fix a specific issue with the least code possible.
7. Document your changes thoroughly.
8. Ensure that none of the tests fail.
9. Be reactive to any comments, reviews or change requests entered in your pull request.
10. It's up to the maintainers discretion whether the PR is accepted or rejected. Remember, the maintainer will have to maintain the code you've added (and fix any issues filed about it) for the years to come! In case of a rejection, the maintainer will explain the reasons guiding the decision.

Thank you for your contributions!
