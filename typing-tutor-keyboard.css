/**
 * Minimal Typing Tu<PERSON> Keyboard Styles
 * Focused on educational use for young children
 */

.typing-tutor-keyboard {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  max-width: 800px;
  margin: 0 auto;
}

.keyboard-row {
  display: flex;
  justify-content: center;
  margin-bottom: 6px;
  gap: 4px;
}

.keyboard-row:last-child {
  margin-bottom: 0;
}

.keyboard-key {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 45px;
  min-width: 45px;
  background: white;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  outline: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.keyboard-key:hover {
  border-color: #bbb;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.keyboard-key:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Normal character keys */
.keyboard-key.normal-key {
  flex: 1;
  max-width: 60px;
}

/* Function keys (Backspace, Enter, etc.) */
.keyboard-key.function-key {
  background: #f8f9fa;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Modifier keys (Shift, Caps Lock, etc.) */
.keyboard-key.modifier-key {
  background: #fff3cd;
  border-color: #ffeaa7;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Space bar */
.keyboard-key.space-key {
  flex: 6;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Specific key sizing */
.keyboard-key[data-key="backspace"] {
  flex: 2.5;
}

.keyboard-key[data-key="tab"] {
  flex: 1.5;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.keyboard-key[data-key="capslock"] {
  flex: 1.8;
}

.keyboard-key[data-key="enter"] {
  flex: 2.2;
}

.keyboard-key[data-key="shiftleft"],
.keyboard-key[data-key="shiftright"] {
  flex: 2.5;
}

/* Highlighted states for physical keyboard sync */
.keyboard-key.highlighted {
  border-width: 3px;
  font-weight: 700;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Normal keys when highlighted (green) */
.keyboard-key.normal-key.highlighted {
  background-color: #4CAF50 !important;
  color: white;
  border-color: #45a049;
}

/* Modifier keys when highlighted (yellow) */
.keyboard-key.modifier-key.highlighted {
  background-color: #FFC107 !important;
  color: #333;
  border-color: #ffb300;
}

/* Function keys when highlighted (blue) */
.keyboard-key.function-key.highlighted {
  background-color: #2196F3 !important;
  color: white;
  border-color: #1976D2;
}

/* Active modifier states */
.keyboard-key.active-modifier {
  background-color: #ff9800;
  color: white;
  border-color: #f57c00;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.keyboard-key.active-modifier.highlighted {
  background-color: #ff6f00 !important;
  border-color: #e65100;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .typing-tutor-keyboard {
    padding: 8px;
    max-width: 100%;
  }
  
  .keyboard-key {
    min-height: 40px;
    min-width: 35px;
    font-size: 14px;
  }
  
  .keyboard-key.function-key,
  .keyboard-key.modifier-key,
  .keyboard-key.space-key {
    font-size: 10px;
  }

  .keyboard-key[data-key="tab"] {
    font-size: 14px;
  }
  
  .keyboard-row {
    gap: 3px;
    margin-bottom: 4px;
  }
}

@media (max-width: 480px) {
  .keyboard-key {
    min-height: 35px;
    min-width: 30px;
    font-size: 12px;
  }
  
  .keyboard-key.function-key,
  .keyboard-key.modifier-key,
  .keyboard-key.space-key {
    font-size: 9px;
  }

  .keyboard-key[data-key="tab"] {
    font-size: 12px;
  }
  
  .keyboard-row {
    gap: 2px;
    margin-bottom: 3px;
  }
}

/* Animation for educational feedback */
@keyframes keyPress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.keyboard-key.highlighted {
  animation: keyPress 0.2s ease;
}

/* Focus styles for accessibility */
.keyboard-key:focus {
  border-color: #2196F3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .keyboard-key {
    border-width: 3px;
    border-color: #000;
  }
  
  .keyboard-key.highlighted {
    border-width: 4px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .keyboard-key {
    transition: none;
  }
  
  .keyboard-key.highlighted {
    animation: none;
  }
}
