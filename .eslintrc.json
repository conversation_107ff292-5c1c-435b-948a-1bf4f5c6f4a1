{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 6, "sourceType": "module", "ecmaFeatures": {"modules": true, "experimentalObjectRestSpread": true}}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "rules": {"comma-dangle": 0, "no-unused-vars": "warn", "no-unexpected-multiline": "warn", "prefer-const": "warn", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-var-requires": "off"}, "settings": {}, "env": {"browser": true, "node": true, "jasmine": true, "jest": true, "es6": true}}